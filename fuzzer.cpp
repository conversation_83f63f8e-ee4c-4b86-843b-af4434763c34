#include "fuzzer.hpp"
#include "corpus.hpp"
#include "coverage.hpp"
#include "injector.hpp"
#include "target_resolver.hpp"

#include <cerrno>
#include <csignal>
#include <cstring>
#include <fstream>
#include <iomanip>
#include <iostream>
#include <sstream>


#ifdef _WIN32
#include <direct.h>
#include <io.h>
#define mkdir(path, mode) _mkdir(path)
// Undefine Windows ERROR macro that conflicts with our enum
#ifdef ERROR
#undef ERROR
#endif
#ifndef SIGSEGV
#define SIGSEGV 11
#endif
#ifndef SIGILL
#define SIGILL 4
#endif
#ifndef SIGFPE
#define SIGFPE 8
#endif
#ifndef SIGALRM
#define SIGALRM 14
#endif
#else
#include <sys/stat.h>
#include <sys/wait.h>
#include <unistd.h>
#endif

// Undefine Windows macros that might conflict with our enums
#ifdef ERROR
#undef ERROR
#endif

namespace fuzzer {

// Static member initialization
Fuzzer *Fuzzer::instance_ = nullptr;

Fuzzer::Fuzzer(const FuzzConfig &config) : config_(config), stats_() {
  instance_ = this;

  // Initialize components
  corpus_ = std::make_unique<Corpus>(config_.corpus_dir);

  // Coverage will be initialized after target attachment
  coverage_ = nullptr;
  target_resolver_ = nullptr;
}

Fuzzer::~Fuzzer() {
  if (is_attached_) {
    stop();
  }
  instance_ = nullptr;
}

bool Fuzzer::initialize() {
  std::cout << "[+] Initializing fuzzer..." << '\n';

  // Setup directories
  if (!setup_directories()) {
    std::cerr << "[-] Failed to setup directories" << '\n';
    return false;
  }

  // Setup signal handlers
  if (!setup_signal_handlers()) {
    std::cerr << "[-] Failed to setup signal handlers" << '\n';
    return false;
  }

  // Initialize corpus
  if (!corpus_->initialize()) {
    std::cerr << "[-] Failed to initialize corpus" << '\n';
    return false;
  }

  // Load seeds if provided
  if (!config_.seed_files.empty()) {
    if (!load_seeds(config_.seed_files)) {
      std::cerr << "[-] Failed to load seed files" << '\n';
      return false;
    }
  }

  std::cout << "[+] Fuzzer initialized successfully" << '\n';
  return true;
}

bool Fuzzer::attach_to_process(uint32_t pid) {
  std::cout << "[+] Attaching to process " << pid << '\n';

  // Create injector
  injector_ = std::make_unique<ProcessInjector>();
  if (!injector_->attach(pid)) {
    std::cerr << "[-] Failed to attach to process " << pid << '\n';
    return false;
  }

  target_pid_ = pid;
  is_attached_ = true;

  // Initialize target resolver
  target_resolver_ = std::make_unique<TargetResolver>(injector_.get());
  if (!target_resolver_->analyze_process_modules()) {
    std::cerr << "[-] Failed to analyze process modules" << '\n';
    return false;
  }

  // Initialize coverage tracking if enabled
  if (config_.track_coverage) {
    coverage_ = std::make_unique<Coverage>(injector_.get());
  }

  std::cout << "[+] Successfully attached to process " << pid << '\n';
  return true;
}

bool Fuzzer::set_target_function(const std::string &function_name) {
  if (!target_resolver_) {
    std::cerr << "[-] Target resolver not initialized" << '\n';
    return false;
  }

  uintptr_t address;
  if (!target_resolver_->resolve_function(function_name, address)) {
    std::cerr << "[-] Failed to resolve function: " << function_name << '\n';
    return false;
  }

  return set_target_function(address);
}

bool Fuzzer::set_target_function(uintptr_t function_address) {
  target_function_addr_ = function_address;

  std::cout << "[+] Target function set to 0x" << std::hex << function_address
            << std::dec << '\n';

  // Initialize coverage for the target function if enabled
  if (coverage_ && config_.track_coverage) {
    if (!coverage_->analyze_function(function_address)) {
      std::cerr << "[-] Failed to analyze target function for coverage" << '\n';
      return false;
    }
  }

  return true;
}

bool Fuzzer::load_seeds(const std::vector<std::string> &seed_files) {
  std::cout << "[+] Loading " << seed_files.size() << " seed files..." << '\n';

  size_t loaded = 0;
  for (const auto &file : seed_files) {
    auto data = read_file(file);
    if (!data.empty()) {
      if (corpus_->add_seed(data, file)) {
        loaded++;
      }
    }
  }

  std::cout << "[+] Loaded " << loaded << " seeds" << '\n';
  return loaded > 0;
}

bool Fuzzer::add_seed(const std::vector<uint8_t> &data) {
  return corpus_->add_seed(data);
}

void Fuzzer::run() {
  std::cout << "[+] Starting fuzzing campaign..." << '\n';

  if (!is_attached_ || target_function_addr_ == 0) {
    std::cerr << "[-] Fuzzer not properly initialized" << '\n';
    return;
  }

  stats_.reset();
  should_stop_ = false;

  // Start coverage tracking
  if (coverage_) {
    coverage_->start_tracking();
  }

  // Main fuzzing loop
  while (!should_stop_ && stats_.total_executions < config_.max_iterations) {
    // Select a seed from corpus
    auto *seed = corpus_->select_seed();
    if (!seed) {
      std::cerr << "[-] No seeds available in corpus" << '\n';
      break;
    }

    // Mutate the seed
    auto mutated_input = mutate_input(seed->data);

    // Execute the input
    auto exec_info = execute_input(mutated_input);

    // Update statistics
    stats_.total_executions++;

    // Handle execution result
    switch (exec_info.result) {
    case ExecutionResult::CRASH:
      stats_.crashes_found++;
      if (is_new_crash(exec_info.crash_hash)) {
        stats_.unique_crashes++;
        save_crash(mutated_input, exec_info);
        std::cout << "[!] New crash found! Hash: " << exec_info.crash_hash
                  << '\n';
      }
      break;

    case ExecutionResult::TIMEOUT:
      stats_.timeouts++;
      break;

    case ExecutionResult::SUCCESS:
      if (exec_info.new_coverage) {
        stats_.new_coverage++;
        save_interesting_input(mutated_input);
        corpus_->add_seed(mutated_input, "coverage");
      }
      break;

    case ExecutionResult::ERROR:
      // Continue fuzzing on errors
      break;
    }

    // Print periodic statistics
    if (stats_.total_executions % 1000 == 0) {
      print_stats();
    }
  }

  // Stop coverage tracking
  if (coverage_) {
    coverage_->stop_tracking();
  }

  std::cout << "[+] Fuzzing campaign completed" << '\n';
  print_stats();
}

void Fuzzer::stop() {
  should_stop_ = true;
  std::cout << "[+] Stopping fuzzer..." << '\n';
}

ExecutionInfo Fuzzer::execute_input(const std::vector<uint8_t> &input) {
  ExecutionInfo info;
  info.result = ExecutionResult::ERROR;

  if (!injector_ || !is_attached_ || target_function_addr_ == 0) {
    std::cerr << "[-] Fuzzer not properly initialized for execution" << '\n';
    return info;
  }

  auto start_time = std::chrono::steady_clock::now();

  try {
    // Call the target function with the input
    uint32_t return_value = 0;
    bool success = injector_->call_function(target_function_addr_, input,
                                            return_value, config_.timeout_ms);

    auto end_time = std::chrono::steady_clock::now();
    info.execution_time_us =
        std::chrono::duration_cast<std::chrono::microseconds>(end_time -
                                                              start_time)
            .count();

    if (success) {
      info.result = ExecutionResult::SUCCESS;

      // Check for new coverage if tracking is enabled
      if (coverage_) {
        info.new_coverage = coverage_->has_new_coverage();
      }

      if (config_.verbose) {
        std::cout << "[DEBUG] Function executed successfully, return value: "
                  << return_value << ", time: " << info.execution_time_us
                  << "us" << '\n';
      }
    } else {
      // Check what kind of failure occurred
      int signal, status;
      if (injector_->wait_for_signal(signal, status, config_.timeout_ms)) {
        if (signal == SIGSEGV || signal == SIGILL || signal == SIGFPE ||
            signal == SIGABRT) {
          info.result = ExecutionResult::CRASH;
          info.signal = signal;
          info.crash_hash = calculate_crash_hash(input, signal);

          if (config_.verbose) {
            std::cout << "[DEBUG] Crash detected with signal " << signal
                      << ", hash: " << info.crash_hash << '\n';
          }
        } else if (signal == SIGALRM) {
          info.result = ExecutionResult::TIMEOUT;

          if (config_.verbose) {
            std::cout << "[DEBUG] Execution timeout after "
                      << config_.timeout_ms << "ms" << '\n';
          }
        } else {
          info.result = ExecutionResult::ERROR;

          if (config_.verbose) {
            std::cout << "[DEBUG] Execution failed with signal " << signal
                      << '\n';
          }
        }
      } else {
        info.result = ExecutionResult::TIMEOUT;

        if (config_.verbose) {
          std::cout << "[DEBUG] Execution timeout (no signal received)" << '\n';
        }
      }
    }

  } catch (const std::exception &e) {
    std::cerr << "[-] Exception during execution: " << e.what() << '\n';
    info.result = ExecutionResult::ERROR;
  }

  return info;
}

std::vector<uint8_t> Fuzzer::mutate_input(const std::vector<uint8_t> &input) {
  // Simple mutation for now - this would be enhanced with the MutationEngine
  std::vector<uint8_t> mutated = input;

  if (mutated.empty()) {
    mutated.push_back(0x41); // Add 'A' if empty
    return mutated;
  }

  // Random mutation
  size_t pos = rand() % mutated.size();
  mutated[pos] = rand() % 256;

  return mutated;
}

std::string Fuzzer::calculate_crash_hash(const std::vector<uint8_t> &input,
                                         uint32_t signal) {
  // Simple hash calculation - would be enhanced with stack trace hashing
  std::string hash =
      "crash_" + std::to_string(signal) + "_" + std::to_string(input.size());
  return hash;
}

bool Fuzzer::is_new_crash(const std::string &crash_hash) {
  return unique_crash_hashes_.find(crash_hash) == unique_crash_hashes_.end();
}

void Fuzzer::save_crash(const std::vector<uint8_t> &input,
                        const ExecutionInfo &exec_info) {
  unique_crash_hashes_.insert(exec_info.crash_hash);

  std::string filename =
      config_.crash_dir + "/crash_" + exec_info.crash_hash + ".bin";
  write_file(filename, input);
}

void Fuzzer::save_interesting_input(const std::vector<uint8_t> &input) {
  std::string filename = config_.corpus_dir + "/interesting_" +
                         std::to_string(stats_.total_executions) + ".bin";
  write_file(filename, input);
}

bool Fuzzer::setup_directories() {
  // Simple directory creation - would be enhanced with recursive creation
  if (mkdir(config_.corpus_dir.c_str(), 0755) != 0 && errno != EEXIST) {
    std::cerr << "[-] Failed to create corpus directory: " << config_.corpus_dir
              << '\n';
    return false;
  }

  if (mkdir(config_.crash_dir.c_str(), 0755) != 0 && errno != EEXIST) {
    std::cerr << "[-] Failed to create crash directory: " << config_.crash_dir
              << '\n';
    return false;
  }

  if (mkdir(config_.output_dir.c_str(), 0755) != 0 && errno != EEXIST) {
    std::cerr << "[-] Failed to create output directory: " << config_.output_dir
              << '\n';
    return false;
  }

  return true;
}

bool Fuzzer::setup_signal_handlers() {
  signal(SIGINT, signal_handler);
  signal(SIGTERM, signal_handler);
  return true;
}

void Fuzzer::signal_handler(int sig) {
  if (instance_) {
    std::cout << "\n[+] Received signal " << sig << ", stopping fuzzer..."
              << '\n';
    instance_->stop();
  }
}

void Fuzzer::print_stats() const {
  std::cout << "\n=== Fuzzing Statistics ===" << '\n';
  std::cout << "Executions: " << stats_.total_executions << '\n';
  std::cout << "Exec/sec: " << std::fixed << std::setprecision(2)
            << stats_.get_exec_per_sec() << '\n';
  std::cout << "Crashes: " << stats_.crashes_found
            << " (unique: " << stats_.unique_crashes << ")" << '\n';
  std::cout << "Timeouts: " << stats_.timeouts << '\n';
  std::cout << "New coverage: " << stats_.new_coverage << '\n';
  std::cout << "=========================" << '\n';
}

// Utility functions
std::vector<uint8_t> read_file(const std::string &filename) {
  std::ifstream file(filename, std::ios::binary);
  if (!file) {
    return {};
  }

  file.seekg(0, std::ios::end);
  size_t size = file.tellg();
  file.seekg(0, std::ios::beg);

  std::vector<uint8_t> data(size);
  file.read(reinterpret_cast<char *>(data.data()), size);

  return data;
}

bool write_file(const std::string &filename, const std::vector<uint8_t> &data) {
  std::ofstream file(filename, std::ios::binary);
  if (!file) {
    return false;
  }

  file.write(reinterpret_cast<const char *>(data.data()), data.size());
  return file.good();
}

std::string bytes_to_hex(const std::vector<uint8_t> &data) {
  std::stringstream ss;
  ss << std::hex << std::setfill('0');
  for (uint8_t byte : data) {
    ss << std::setw(2) << static_cast<int>(byte);
  }
  return ss.str();
}

std::vector<uint8_t> hex_to_bytes(const std::string &hex) {
  std::vector<uint8_t> bytes;
  for (size_t i = 0; i < hex.length(); i += 2) {
    std::string byte_str = hex.substr(i, 2);
    uint8_t byte =
        static_cast<uint8_t>(std::strtol(byte_str.c_str(), nullptr, 16));
    bytes.push_back(byte);
  }
  return bytes;
}

} // namespace fuzzer
