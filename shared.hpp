#pragma once

#include <chrono>
#include <string>
#include <vector>


namespace fuzzer {

struct FuzzStats {
  uint64_t total_executions = 0;
  uint64_t crashes_found = 0;
  uint64_t unique_crashes = 0;
  uint64_t timeouts = 0;
  uint64_t new_coverage = 0;
  std::chrono::steady_clock::time_point start_time;

  void reset() {
    total_executions = 0;
    crashes_found = 0;
    unique_crashes = 0;
    timeouts = 0;
    new_coverage = 0;
    start_time = std::chrono::steady_clock::now();
  }

  double get_exec_per_sec() const {
    auto now = std::chrono::steady_clock::now();
    auto duration =
        std::chrono::duration_cast<std::chrono::seconds>(now - start_time);
    return duration.count() > 0
               ? static_cast<double>(total_executions) / duration.count()
               : 0.0;
  }
};

struct FuzzConfig {
  std::string target_binary;
  std::string target_function;
  uint32_t target_pid = 0;
  std::string corpus_dir = "./corpus";
  std::string crash_dir = "./crashes";
  std::string output_dir = "./output";
  uint32_t max_input_size = 4096;
  uint32_t timeout_ms = 1000;
  uint32_t max_iterations = 1000000;
  bool verbose = false;
  bool minimize_crashes = true;
  bool track_coverage = true;
  uint32_t mutation_depth = 8;
  std::vector<std::string> seed_files;
};

enum ExecutionResult { SUCCESS, CRASH, TIMEOUT, ERROR };

struct ExecutionInfo {
  ExecutionResult result;
  uint32_t signal = 0;
  uint64_t execution_time_us = 0;
  bool new_coverage = false;
  std::string crash_hash;
};
} // namespace fuzzer