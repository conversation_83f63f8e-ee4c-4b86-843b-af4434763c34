#pragma once

#include <cstdint>
#include <cstring>
#include <string>
#include <unordered_map>
#include <vector>

// Include system headers first
#include <sys/types.h>

#ifdef _WIN32
#include <windows.h>
// Define user_regs_struct for Windows
struct user_regs_struct {
  uint32_t eax, ebx, ecx, edx, esi, edi, ebp, esp, eip;
};
#else
#include <sys/user.h>
#endif

namespace fuzzer {

// Type alias for process ID to avoid conflicts
using process_id_t = int;

struct ProcessInfo {
  process_id_t pid;
  std::string name;
  std::string path;
  uintptr_t base_address;
  std::vector<std::pair<uintptr_t, uintptr_t>> memory_regions;
};

class ProcessInjector {
public:
  ProcessInjector() = default;
  ~ProcessInjector();

  // Process attachment
  bool attach(process_id_t pid);
  bool detach();
  bool is_attached() const { return attached_pid_ != -1; }
  process_id_t get_attached_pid() const { return attached_pid_; }

  // Process information
  static std::vector<ProcessInfo> list_processes();
  static ProcessInfo get_process_info(process_id_t pid);
  bool get_memory_maps(std::vector<std::pair<uintptr_t, uintptr_t>> &regions);

  // Memory operations
  bool read_memory(uintptr_t address, void *buffer, size_t size);
  bool write_memory(uintptr_t address, const void *data, size_t size);

  // Code injection
  bool inject_shellcode(uintptr_t address,
                        const std::vector<uint8_t> &shellcode);
  bool inject_library(const std::string &library_path);

  // Function hooking
  bool hook_function(uintptr_t function_address, uintptr_t hook_address);
  bool unhook_function(uintptr_t function_address);

  // Execution control
  bool continue_execution();
  bool single_step();
  bool set_breakpoint(uintptr_t address);
  bool remove_breakpoint(uintptr_t address);

  // Register access
  bool get_registers(struct user_regs_struct &regs);
  bool set_registers(const struct user_regs_struct &regs);

  // Signal handling
  bool wait_for_signal(int &signal, int &status, uint32_t timeout_ms = 1000);
  bool send_signal(int signal);

  // Function execution
  bool call_function(uintptr_t function_address,
                     const std::vector<uint8_t> &args, uint32_t &return_value,
                     uint32_t timeout_ms = 1000);
  bool setup_function_call(uintptr_t function_address, const void *arg1,
                           size_t arg1_size);
  bool execute_and_wait(uint32_t timeout_ms, int &exit_signal);

  // Memory allocation in target process
  uintptr_t allocate_memory_in_target(size_t size);
  bool free_memory_in_target(uintptr_t address, size_t size);

private:
  process_id_t attached_pid_ = -1;
  std::unordered_map<uintptr_t, uint8_t> original_bytes_;

  // Internal helpers
  bool ptrace_attach(process_id_t pid);
  bool ptrace_detach(process_id_t pid);
  uintptr_t find_free_memory_region(size_t size);
  bool allocate_memory(uintptr_t &address, size_t size);
  bool free_memory(uintptr_t address, size_t size);

  // Architecture-specific helpers for 32-bit
  static constexpr size_t WORD_SIZE = 4;
  static constexpr uint8_t INT3_OPCODE = 0xCC;

  bool read_word(uintptr_t address, uint32_t &value);
  bool write_word(uintptr_t address, uint32_t value);
};

// ELF parsing utilities for 32-bit binaries
class ELF32Parser {
public:
  explicit ELF32Parser(const std::string &binary_path);
  ~ELF32Parser() = default;

  bool parse();
  bool is_valid() const { return valid_; }

  // Symbol resolution
  uintptr_t get_symbol_address(const std::string &symbol_name);
  std::vector<std::string> get_exported_symbols();

  // Section information
  uintptr_t get_section_address(const std::string &section_name);
  size_t get_section_size(const std::string &section_name);

  // Entry point and base address
  uintptr_t get_entry_point() const { return entry_point_; }
  uintptr_t get_base_address() const { return base_address_; }

private:
  std::string binary_path_;
  bool valid_ = false;
  uintptr_t entry_point_ = 0;
  uintptr_t base_address_ = 0;

  // ELF structures (simplified for 32-bit)
  struct Symbol {
    std::string name;
    uintptr_t address;
    size_t size;
    uint8_t type;
    uint8_t binding;
  };

  struct Section {
    std::string name;
    uintptr_t address;
    size_t size;
    uint32_t type;
  };

  std::vector<Symbol> symbols_;
  std::vector<Section> sections_;

  bool parse_elf_header();
  bool parse_section_headers();
  bool parse_symbol_table();
  bool parse_dynamic_symbols();
};

// Utility functions
std::string get_process_name(process_id_t pid);
std::string get_process_path(process_id_t pid);
std::vector<process_id_t> find_processes_by_name(const std::string &name);
bool is_process_running(process_id_t pid);
uintptr_t get_module_base_address(process_id_t pid,
                                  const std::string &module_name);

} // namespace fuzzer
