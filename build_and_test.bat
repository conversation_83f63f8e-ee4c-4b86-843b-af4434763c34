@echo off
REM Build and test script for the fuzzer

echo ===============================================
echo Linux 32-bit Process Fuzzer - Build and Test
echo ===============================================

REM Check if we're in the right directory
if not exist "fuzzer.hpp" (
    echo Error: fuzzer.hpp not found. Please run this script from the project directory.
    pause
    exit /b 1
)

echo.
echo [1/5] Checking build environment...

REM Check for g++ compiler
g++ --version >nul 2>&1
if errorlevel 1 (
    echo Error: g++ compiler not found. Please install MinGW-w64 or similar.
    echo You can download it from: https://www.mingw-w64.org/
    pause
    exit /b 1
)

echo Build environment OK

echo.
echo [2/5] Creating output directories...
if not exist corpus mkdir corpus
if not exist crashes mkdir crashes
if not exist output mkdir output
echo Directories created

echo.
echo [3/5] Building fuzzer...

REM Compile the fuzzer (Note: This will likely fail on Windows due to Linux-specific code)
echo Compiling main.cpp...
g++ -std=c++17 -Wall -Wextra -O2 -g -c main.cpp -o main.o 2>build_errors.txt
if errorlevel 1 (
    echo Warning: main.cpp compilation failed. This is expected on Windows.
    echo The fuzzer is designed for Linux systems.
    echo See build_errors.txt for details.
) else (
    echo main.cpp compiled successfully
)

echo Compiling fuzzer.cpp...
g++ -std=c++17 -Wall -Wextra -O2 -g -c fuzzer.cpp -o fuzzer.o 2>>build_errors.txt
if errorlevel 1 (
    echo Warning: fuzzer.cpp compilation failed. This is expected on Windows.
) else (
    echo fuzzer.cpp compiled successfully
)

echo Compiling injector.cpp...
g++ -std=c++17 -Wall -Wextra -O2 -g -c injector.cpp -o injector.o 2>>build_errors.txt
if errorlevel 1 (
    echo Warning: injector.cpp compilation failed. This is expected on Windows.
) else (
    echo injector.cpp compiled successfully
)

echo Compiling corpus.cpp...
g++ -std=c++17 -Wall -Wextra -O2 -g -c corpus.cpp -o corpus.o 2>>build_errors.txt
if errorlevel 1 (
    echo Warning: corpus.cpp compilation failed. This is expected on Windows.
) else (
    echo corpus.cpp compiled successfully
)

REM Try to link if all objects compiled
if exist main.o if exist fuzzer.o if exist injector.o if exist corpus.o (
    echo Linking fuzzer...
    g++ -o fuzzer.exe main.o fuzzer.o injector.o corpus.o -lpthread 2>>build_errors.txt
    if errorlevel 1 (
        echo Warning: Linking failed. This is expected on Windows.
    ) else (
        echo Fuzzer linked successfully!
    )
)

echo.
echo [4/5] Building test target...
g++ -std=c++17 -Wall -Wextra -O2 -g -o test_target.exe test_target.cpp 2>>build_errors.txt
if errorlevel 1 (
    echo Warning: Test target compilation failed.
) else (
    echo Test target compiled successfully!
)

echo.
echo [5/5] Creating seed files...
call create_seeds.bat

echo.
echo ===============================================
echo Build Summary
echo ===============================================

if exist fuzzer.exe (
    echo [SUCCESS] Fuzzer executable: fuzzer.exe
) else (
    echo [EXPECTED] Fuzzer not built (Linux-specific code)
)

if exist test_target.exe (
    echo [SUCCESS] Test target: test_target.exe
) else (
    echo [FAILED] Test target not built
)

if exist corpus\seed_hello.bin (
    echo [SUCCESS] Seed files created in corpus\
) else (
    echo [FAILED] Seed files not created
)

echo.
echo ===============================================
echo Next Steps
echo ===============================================
echo.
echo This fuzzer is designed for Linux 32-bit systems.
echo To use it properly:
echo.
echo 1. Transfer the source code to a Linux system
echo 2. Install build dependencies:
echo    sudo apt-get install build-essential gcc-multilib g++-multilib
echo.
echo 3. Build on Linux:
echo    make
echo.
echo 4. Run the test target:
echo    ./test_target ^&
echo.
echo 5. Run the fuzzer:
echo    ./fuzzer -p ^<PID^> -f fuzz_target
echo.
echo For Windows development, consider:
echo - Using WSL (Windows Subsystem for Linux)
echo - Using a Linux virtual machine
echo - Adapting the code for Windows APIs

if exist build_errors.txt (
    echo.
    echo Build errors logged to: build_errors.txt
)

echo.
pause
