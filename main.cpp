#include "fuzzer.hpp"
#include "injector.hpp"
#include "shared.hpp"

#include <cstdlib>
#include <iostream>
#include <string>
#include <vector>

void print_usage(const char *program_name) {
  std::cout
      << "Usage: " << program_name << " [options]\n"
      << "\nOptions:\n"
      << "  -p, --pid PID              Target process PID\n"
      << "  -b, --binary PATH          Target binary path\n"
      << "  -f, --function NAME        Target function name\n"
      << "  -a, --address ADDR         Target function address (hex)\n"
      << "  -c, --corpus DIR           Corpus directory (default: ./corpus)\n"
      << "  -o, --output DIR           Output directory (default: ./output)\n"
      << "  -s, --seed FILE            Seed file(s) to load\n"
      << "  -i, --iterations NUM       Maximum iterations (default: 1000000)\n"
      << "  -t, --timeout MS           Timeout in milliseconds (default: "
         "1000)\n"
      << "  -v, --verbose              Verbose output\n"
      << "  --no-coverage              Disable coverage tracking\n"
      << "  -h, --help                 Show this help\n"
      << "\nExamples:\n"
      << "  " << program_name << " -p 1234 -f vulnerable_function\n"
      << "  " << program_name
      << " -b /bin/target -f main -s seed1.bin -s seed2.bin\n"
      << "  " << program_name << " -p 1234 -a 0x08048000 -c ./my_corpus\n"
      << '\n';
}

void print_banner() {
  std::cout << R"(
 ███████╗██╗   ██╗███████╗███████╗███████╗██████╗
 ██╔════╝██║   ██║╚══███╔╝╚══███╔╝██╔════╝██╔══██╗
 █████╗  ██║   ██║  ███╔╝   ███╔╝ █████╗  ██████╔╝
 ██╔══╝  ██║   ██║ ███╔╝   ███╔╝  ██╔══╝  ██╔══██╗
 ██║     ╚██████╔╝███████╗███████╗███████╗██║  ██║
 ╚═╝      ╚═════╝ ╚══════╝╚══════╝╚══════╝╚═╝  ╚═╝

 Linux 32-bit Process Fuzzer v1.0
 Supports process injection and function targeting
)" << '\n';
}

bool parse_arguments(int argc, char *argv[], fuzzer::FuzzConfig &config) {
  for (int i = 1; i < argc; ++i) {
    std::string arg = argv[i];

    if (arg == "-h" || arg == "--help") {
      return false;
    } else if (arg == "-p" || arg == "--pid") {
      if (i + 1 >= argc) {
        std::cerr << "[-] Missing PID argument" << '\n';
        return false;
      }
      config.target_pid = std::stoul(argv[++i]);
    } else if (arg == "-b" || arg == "--binary") {
      if (i + 1 >= argc) {
        std::cerr << "[-] Missing binary path argument" << '\n';
        return false;
      }
      config.target_binary = argv[++i];
    } else if (arg == "-f" || arg == "--function") {
      if (i + 1 >= argc) {
        std::cerr << "[-] Missing function name argument" << '\n';
        return false;
      }
      config.target_function = argv[++i];
    } else if (arg == "-a" || arg == "--address") {
      if (i + 1 >= argc) {
        std::cerr << "[-] Missing address argument" << '\n';
        return false;
      }
      // Parse hex address
      std::string addr_str = argv[++i];
      if (addr_str.substr(0, 2) == "0x" || addr_str.substr(0, 2) == "0X") {
        addr_str = addr_str.substr(2);
      }
      config.target_function = "0x" + addr_str;
    } else if (arg == "-c" || arg == "--corpus") {
      if (i + 1 >= argc) {
        std::cerr << "[-] Missing corpus directory argument" << '\n';
        return false;
      }
      config.corpus_dir = argv[++i];
    } else if (arg == "-o" || arg == "--output") {
      if (i + 1 >= argc) {
        std::cerr << "[-] Missing output directory argument" << '\n';
        return false;
      }
      config.output_dir = argv[++i];
      config.crash_dir = config.output_dir + "/crashes";
    } else if (arg == "-s" || arg == "--seed") {
      if (i + 1 >= argc) {
        std::cerr << "[-] Missing seed file argument" << '\n';
        return false;
      }
      config.seed_files.push_back(argv[++i]);
    } else if (arg == "-i" || arg == "--iterations") {
      if (i + 1 >= argc) {
        std::cerr << "[-] Missing iterations argument" << '\n';
        return false;
      }
      config.max_iterations = std::stoul(argv[++i]);
    } else if (arg == "-t" || arg == "--timeout") {
      if (i + 1 >= argc) {
        std::cerr << "[-] Missing timeout argument" << '\n';
        return false;
      }
      config.timeout_ms = std::stoul(argv[++i]);
    } else if (arg == "-v" || arg == "--verbose") {
      config.verbose = true;
    } else if (arg == "--no-coverage") {
      config.track_coverage = false;
    } else {
      std::cerr << "[-] Unknown argument: " << arg << '\n';
      return false;
    }
  }

  return true;
}

bool validate_config(const fuzzer::FuzzConfig &config) {
  if (config.target_pid == 0 && config.target_binary.empty()) {
    std::cerr << "[-] Must specify either target PID or binary path" << '\n';
    return false;
  }

  if (config.target_function.empty()) {
    std::cerr << "[-] Must specify target function name or address" << '\n';
    return false;
  }

  if (config.target_pid != 0) {
    if (!fuzzer::is_process_running(config.target_pid)) {
      std::cerr << "[-] Target process " << config.target_pid
                << " is not running" << '\n';
      return false;
    }
  }

  return true;
}

void print_config(const fuzzer::FuzzConfig &config) {
  std::cout << "\n=== Fuzzer Configuration ===\n";

  if (config.target_pid != 0) {
    std::cout << "Target PID: " << config.target_pid << '\n';
    std::cout << "Process name: " << fuzzer::get_process_name(config.target_pid)
              << '\n';
  }

  if (!config.target_binary.empty()) {
    std::cout << "Target binary: " << config.target_binary << '\n';
  }

  std::cout << "Target function: " << config.target_function << '\n';
  std::cout << "Corpus directory: " << config.corpus_dir << '\n';
  std::cout << "Crash directory: " << config.crash_dir << '\n';
  std::cout << "Max iterations: " << config.max_iterations << '\n';
  std::cout << "Timeout: " << config.timeout_ms << "ms" << '\n';
  std::cout << "Coverage tracking: "
            << (config.track_coverage ? "enabled" : "disabled") << '\n';

  if (!config.seed_files.empty()) {
    std::cout << "Seed files: ";
    for (const auto &seed : config.seed_files) {
      std::cout << seed << " ";
    }
    std::cout << '\n';
  }

  std::cout << "============================" << '\n';
}

int main(int argc, char *argv[]) {
  print_banner();

  fuzzer::FuzzConfig config;

  if (!parse_arguments(argc, argv, config)) {
    print_usage(argv[0]);
    return 1;
  }

  if (!validate_config(config)) {
    return 1;
  }

  print_config(config);

  // Create and initialize fuzzer
  fuzzer::Fuzzer fuzzer(config);

  if (!fuzzer.initialize()) {
    std::cerr << "[-] Failed to initialize fuzzer" << '\n';
    return 1;
  }

  // Attach to target process
  if (config.target_pid != 0) {
    if (!fuzzer.attach_to_process(config.target_pid)) {
      std::cerr << "[-] Failed to attach to target process" << '\n';
      return 1;
    }
  } else {
    // TODO: Launch target binary
    std::cerr << "[-] Binary launching not implemented yet" << '\n';
    return 1;
  }

  // Set target function
  if (config.target_function.substr(0, 2) == "0x") {
    // Parse as hex address
    std::string addr_str = config.target_function.substr(2);
    uintptr_t address = std::stoul(addr_str, nullptr, 16);
    if (!fuzzer.set_target_function(address)) {
      std::cerr << "[-] Failed to set target function address" << '\n';
      return 1;
    }
  } else {
    // Resolve function name
    if (!fuzzer.set_target_function(config.target_function)) {
      std::cerr << "[-] Failed to resolve target function" << '\n';
      return 1;
    }
  }

  std::cout << "\n[+] Starting fuzzing campaign..." << '\n';
  std::cout << "[+] Press Ctrl+C to stop\n" << '\n';

  // Start fuzzing
  fuzzer.run();

  std::cout << "\n[+] Fuzzing completed" << '\n';
  fuzzer.print_stats();

  return 0;
}
