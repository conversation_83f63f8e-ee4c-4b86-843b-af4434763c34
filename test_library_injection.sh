#!/bin/bash

# Test script for library injection fuzzer
# This script demonstrates the new library injection approach

set -e

echo "=============================================="
echo "Library Injection Fuzzer Test"
echo "=============================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're on a supported platform
if [[ "$OSTYPE" == "linux-gnu"* ]]; then
    print_status "Running on Linux - full support available"
elif [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "cygwin" ]]; then
    print_warning "Running on Windows - limited support"
else
    print_warning "Running on unsupported platform: $OSTYPE"
fi

# Check for required tools
print_status "Checking build environment..."

if ! command -v g++ &> /dev/null; then
    print_error "g++ compiler not found"
    exit 1
fi

if ! command -v make &> /dev/null; then
    print_error "make not found"
    exit 1
fi

print_success "Build environment OK"

# Build the project
print_status "Building library injection fuzzer..."

if make clean > /dev/null 2>&1; then
    print_success "Clean successful"
else
    print_warning "Clean failed (may be normal)"
fi

# Try to build the shared library
print_status "Building shared library..."
if make libfuzz_agent.so > build_lib.log 2>&1; then
    print_success "Shared library built successfully"
else
    print_error "Failed to build shared library"
    echo "Build log:"
    cat build_lib.log
    exit 1
fi

# Try to build the library fuzzer
print_status "Building library fuzzer..."
if make lib_fuzzer > build_fuzzer.log 2>&1; then
    print_success "Library fuzzer built successfully"
else
    print_error "Failed to build library fuzzer"
    echo "Build log:"
    cat build_fuzzer.log
    exit 1
fi

# Build test target
print_status "Building test target..."
if make test_target > build_target.log 2>&1; then
    print_success "Test target built successfully"
else
    print_error "Failed to build test target"
    echo "Build log:"
    cat build_target.log
    exit 1
fi

# Check if files were created
print_status "Verifying build outputs..."

if [[ -f "libfuzz_agent.so" ]]; then
    print_success "Agent library: libfuzz_agent.so"
    ls -la libfuzz_agent.so
else
    print_error "Agent library not found"
    exit 1
fi

if [[ -f "lib_fuzzer" ]]; then
    print_success "Library fuzzer: lib_fuzzer"
    ls -la lib_fuzzer
else
    print_error "Library fuzzer not found"
    exit 1
fi

if [[ -f "test_target" ]]; then
    print_success "Test target: test_target"
    ls -la test_target
else
    print_error "Test target not found"
    exit 1
fi

# Create test directories
print_status "Creating test directories..."
mkdir -p test_corpus test_output test_crashes

# Create test seeds
print_status "Creating test seeds..."
echo -n "Hello" > test_corpus/seed1.bin
echo -n "AAAAAAAAAAAAAAAA" > test_corpus/seed2.bin
echo -n "Test" > test_corpus/seed3.bin
printf "\x41\x41\x41\x41\x42\x42\x42\x42" > test_corpus/seed4.bin

print_success "Created $(ls test_corpus/*.bin | wc -l) test seeds"

# Test the library fuzzer help
print_status "Testing library fuzzer help..."
if ./lib_fuzzer --help > /dev/null 2>&1; then
    print_success "Help command works"
else
    print_warning "Help command failed (may be normal)"
fi

# Start test target in background
print_status "Starting test target..."
./test_target > test_target.log 2>&1 &
TARGET_PID=$!

# Give it time to start
sleep 2

# Check if target is running
if kill -0 $TARGET_PID 2>/dev/null; then
    print_success "Test target started with PID: $TARGET_PID"
else
    print_error "Test target failed to start"
    exit 1
fi

# Test library injection (short run)
print_status "Testing library injection fuzzer..."
echo "Running: ./lib_fuzzer -p $TARGET_PID -f fuzz_target -i 100 -v"

if timeout 30 ./lib_fuzzer -p $TARGET_PID -f fuzz_target -i 100 -v > fuzzer_test.log 2>&1; then
    print_success "Library fuzzer test completed"
else
    FUZZER_EXIT_CODE=$?
    if [[ $FUZZER_EXIT_CODE -eq 124 ]]; then
        print_warning "Fuzzer test timed out (may be normal)"
    else
        print_error "Fuzzer test failed with exit code: $FUZZER_EXIT_CODE"
        echo "Fuzzer log:"
        tail -20 fuzzer_test.log
    fi
fi

# Show fuzzer output
print_status "Fuzzer output (last 10 lines):"
tail -10 fuzzer_test.log

# Test with address instead of function name
print_status "Testing with function address..."
if timeout 10 ./lib_fuzzer -p $TARGET_PID -a 0x08048000 -i 10 > fuzzer_addr_test.log 2>&1; then
    print_success "Address-based fuzzing test completed"
else
    print_warning "Address-based fuzzing test failed (expected on some systems)"
fi

# Test with custom seeds
print_status "Testing with custom seeds..."
if timeout 10 ./lib_fuzzer -p $TARGET_PID -f fuzz_target -c test_corpus -i 50 > fuzzer_seeds_test.log 2>&1; then
    print_success "Seed-based fuzzing test completed"
else
    print_warning "Seed-based fuzzing test failed"
fi

# Clean up
print_status "Cleaning up..."
if kill -0 $TARGET_PID 2>/dev/null; then
    kill $TARGET_PID
    wait $TARGET_PID 2>/dev/null || true
    print_success "Test target terminated"
fi

# Check for shared memory cleanup
if ls /dev/shm/fuzz_agent_* 2>/dev/null; then
    print_warning "Shared memory segments still exist:"
    ls -la /dev/shm/fuzz_agent_* 2>/dev/null || true
    print_status "Cleaning up shared memory..."
    rm -f /dev/shm/fuzz_agent_* 2>/dev/null || true
fi

# Summary
echo ""
echo "=============================================="
echo "Test Summary"
echo "=============================================="

if [[ -f "libfuzz_agent.so" ]] && [[ -f "lib_fuzzer" ]] && [[ -f "test_target" ]]; then
    print_success "All components built successfully"
else
    print_error "Some components failed to build"
fi

if [[ -f "fuzzer_test.log" ]]; then
    EXEC_COUNT=$(grep -o "Executions: [0-9]*" fuzzer_test.log | tail -1 | cut -d' ' -f2)
    if [[ -n "$EXEC_COUNT" ]] && [[ "$EXEC_COUNT" -gt 0 ]]; then
        print_success "Fuzzer executed $EXEC_COUNT test cases"
    else
        print_warning "Fuzzer execution count unclear"
    fi
    
    CRASH_COUNT=$(grep -c "Crash found" fuzzer_test.log || echo "0")
    if [[ "$CRASH_COUNT" -gt 0 ]]; then
        print_success "Found $CRASH_COUNT crashes"
    else
        print_status "No crashes found (may be normal for short test)"
    fi
fi

echo ""
print_status "Test files created:"
echo "  - libfuzz_agent.so (agent library)"
echo "  - lib_fuzzer (main fuzzer)"
echo "  - test_target (vulnerable target)"
echo "  - test_corpus/ (test seeds)"
echo "  - *.log (test logs)"

echo ""
print_status "To run manually:"
echo "  1. Start target: ./test_target &"
echo "  2. Get PID: TARGET_PID=\$!"
echo "  3. Run fuzzer: ./lib_fuzzer -p \$TARGET_PID -f fuzz_target"

echo ""
print_success "Library injection fuzzer test completed!"

# Clean up log files
rm -f build_*.log

exit 0
