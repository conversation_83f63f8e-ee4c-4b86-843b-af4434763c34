#pragma once

#include <cstdint>
#include <vector>
#include <unordered_set>
#include <unordered_map>
#include <memory>
#include <string>

namespace fuzzer {

// Forward declaration
class ProcessInjector;

struct BasicBlock {
    uintptr_t start_address;
    uintptr_t end_address;
    size_t size;
    bool is_hit = false;
    uint64_t hit_count = 0;
};

struct Edge {
    uintptr_t from_address;
    uintptr_t to_address;
    bool is_hit = false;
    uint64_t hit_count = 0;

    bool operator==(const Edge& other) const {
        return from_address == other.from_address && to_address == other.to_address;
    }
};

struct EdgeHash {
    size_t operator()(const Edge& edge) const {
        return std::hash<uintptr_t>()(edge.from_address) ^
               (std::hash<uintptr_t>()(edge.to_address) << 1);
    }
};

class Coverage {
public:
    explicit Coverage(ProcessInjector* injector);
    ~Coverage();

    // Coverage initialization
    bool initialize(uintptr_t start_address, uintptr_t end_address);
    bool analyze_function(uintptr_t function_address);
    bool analyze_module(const std::string& module_name);

    // Coverage tracking
    bool start_tracking();
    bool stop_tracking();
    bool reset_coverage();

    // Coverage information
    bool has_new_coverage();
    size_t get_total_blocks() const { return basic_blocks_.size(); }
    size_t get_covered_blocks() const;
    size_t get_total_edges() const { return edges_.size(); }
    size_t get_covered_edges() const;
    double get_coverage_percentage() const;

    // Coverage bitmap (AFL-style)
    const std::vector<uint8_t>& get_coverage_bitmap() const { return coverage_bitmap_; }
    bool compare_coverage(const std::vector<uint8_t>& other_bitmap);

    // Breakpoint management
    bool set_coverage_breakpoints();
    bool remove_coverage_breakpoints();
    bool handle_breakpoint(uintptr_t address);

    // Coverage export/import
    bool save_coverage(const std::string& filename);
    bool load_coverage(const std::string& filename);

    // Statistics
    void print_coverage_stats() const;
    std::vector<uintptr_t> get_uncovered_blocks() const;
    std::vector<Edge> get_uncovered_edges() const;

private:
    ProcessInjector* injector_;

    // Coverage data structures
    std::vector<BasicBlock> basic_blocks_;
    std::unordered_set<Edge, EdgeHash> edges_;
    std::unordered_map<uintptr_t, size_t> address_to_block_;
    std::unordered_map<uintptr_t, std::vector<size_t>> address_to_edges_;

    // Coverage bitmap (AFL-style, 64KB)
    static constexpr size_t BITMAP_SIZE = 65536;
    std::vector<uint8_t> coverage_bitmap_;
    std::vector<uint8_t> previous_bitmap_;

    // Breakpoint tracking
    std::unordered_map<uintptr_t, uint8_t> original_bytes_;
    std::unordered_set<uintptr_t> active_breakpoints_;

    // Coverage state
    bool tracking_enabled_ = false;
    uintptr_t last_block_address_ = 0;

    // Internal methods
    bool disassemble_function(uintptr_t function_address);
    bool find_basic_blocks(uintptr_t start_address, uintptr_t end_address);
    bool analyze_control_flow();
    void update_coverage_bitmap(uintptr_t from_addr, uintptr_t to_addr);

    // Disassembly helpers (simplified for 32-bit x86)
    struct Instruction {
        uintptr_t address;
        size_t length;
        bool is_branch;
        bool is_call;
        bool is_return;
        uintptr_t target_address;
    };

    bool decode_instruction(uintptr_t address, Instruction& instr);
    bool is_branch_instruction(uint8_t opcode);
    bool is_call_instruction(uint8_t opcode);
    bool is_return_instruction(uint8_t opcode);
    uintptr_t get_branch_target(uintptr_t address, const uint8_t* bytes, size_t length);

    // Coverage calculation
    uint32_t hash_addresses(uintptr_t addr1, uintptr_t addr2);
    void mark_block_covered(size_t block_index);
    void mark_edge_covered(const Edge& edge);
};

// Simple x86-32 disassembler for coverage analysis
class SimpleDisassembler {
public:
    SimpleDisassembler() = default;
    ~SimpleDisassembler() = default;

    struct DisasmResult {
        bool success;
        size_t instruction_length;
        bool is_branch;
        bool is_call;
        bool is_return;
        bool is_conditional;
        uintptr_t target_address;
        std::string mnemonic;
    };

    static DisasmResult disassemble_at(ProcessInjector* injector, uintptr_t address);
    static std::vector<uintptr_t> find_function_boundaries(ProcessInjector* injector,
                                                          uintptr_t start_address);

private:
    // x86-32 instruction analysis
    static bool analyze_instruction(const uint8_t* bytes, size_t max_length,
                                   uintptr_t address, DisasmResult& result);
    static size_t get_instruction_length(const uint8_t* bytes, size_t max_length);
    static bool is_branch_opcode(uint8_t opcode);
    static bool is_call_opcode(uint8_t opcode);
    static bool is_return_opcode(uint8_t opcode);
    static bool is_conditional_branch(uint8_t opcode);
    static uintptr_t calculate_relative_target(uintptr_t address,
                                              const uint8_t* bytes, size_t offset);
};

} // namespace fuzzer
