start_time        : 1750302132
last_update       : 1750303565
run_time          : 1432
fuzzer_pid        : 53319
cycles_done       : 290
cycles_wo_finds   : 290
time_wo_finds     : 0
fuzz_time         : 1432
calibration_time  : 0
cmplog_time       : 0
sync_time         : 0
trim_time         : 0
execs_done        : 638944
execs_per_sec     : 446.01
execs_ps_last_min : 482.88
corpus_count      : 20
corpus_favored    : 1
corpus_found      : 0
corpus_imported   : 0
corpus_variable   : 0
max_depth         : 1
cur_item          : 6
pending_favs      : 0
pending_total     : 0
stability         : 100.00%
bitmap_cvg        : 23.01%
saved_crashes     : 0
saved_hangs       : 0
total_tmout       : 13
last_find         : 0
last_crash        : 0
last_hang         : 0
execs_since_crash : 638944
exec_timeout      : 20
slowest_exec_ms   : 0
peak_rss_mb       : 0
cpu_affinity      : 0
edges_found       : 26
total_edges       : 113
var_byte_count    : 0
havoc_expansion   : 5
auto_dict_entries : 0
testcache_size    : 2328
testcache_count   : 20
testcache_evict   : 0
afl_banner        : ./test_target
afl_version       : ++4.33a
target_mode       : shmem_testcase default
command_line      : afl-fuzz -i /tmp/corpus -o out ./test_target -t
