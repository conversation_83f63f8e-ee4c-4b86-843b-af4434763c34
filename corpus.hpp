#pragma once

#include <cstdint>
#include <memory>
#include <random>
#include <string>
#include <unordered_map>
#include <vector>

namespace fuzzer {

struct SeedInfo {
  std::string filename;
  std::vector<uint8_t> data;
  uint64_t executions = 0;
  uint64_t crashes = 0;
  uint64_t new_coverage = 0;
  double fitness_score = 0.0;
  std::string hash;
  bool is_favorite = false;

  // Reproduction information
  uint32_t seed_value = 0;
  std::vector<uint32_t> mutation_sequence;
};

class Corpus {
public:
  explicit Corpus(const std::string &corpus_dir);
  ~Corpus() = default;

  // Corpus management
  bool initialize();
  bool load_seeds_from_directory();
  bool add_seed(const std::vector<uint8_t> &data,
                const std::string &source = "");
  bool save_seed(const SeedInfo &seed);

  // Seed selection
  SeedInfo *select_seed();
  SeedInfo *select_favorite_seed();
  std::vector<SeedInfo *> get_all_seeds();

  // Corpus statistics
  size_t size() const { return seeds_.size(); }
  size_t get_total_executions() const;
  size_t get_favorite_count() const;

  // Seed management
  bool update_seed_stats(const std::string &hash, uint64_t executions,
                         uint64_t crashes, uint64_t new_coverage);
  bool mark_as_favorite(const std::string &hash);
  bool remove_seed(const std::string &hash);

  // Minimization
  bool minimize_corpus();
  bool minimize_seed(SeedInfo &seed);

  // Reproduction
  bool save_reproduction_info(const std::string &hash, uint32_t seed_value,
                              const std::vector<uint32_t> &mutation_sequence);
  bool load_reproduction_info(const std::string &hash, uint32_t &seed_value,
                              std::vector<uint32_t> &mutation_sequence);

  // Dictionary support
  bool load_dictionary(const std::string &dict_file);
  const std::vector<std::vector<uint8_t>> &get_dictionary() const {
    return dictionary_;
  }

private:
  std::string corpus_dir_;
  std::vector<std::unique_ptr<SeedInfo>> seeds_;
  std::unordered_map<std::string, size_t> hash_to_index_;
  std::vector<std::vector<uint8_t>> dictionary_;

  // Random number generation
  std::random_device rd_;
  std::mt19937 gen_;

  // Internal helpers
  std::string calculate_hash(const std::vector<uint8_t> &data);
  bool is_duplicate(const std::string &hash);
  std::string generate_filename();
  bool save_to_file(const std::string &filename,
                    const std::vector<uint8_t> &data);
  std::vector<uint8_t> load_from_file(const std::string &filename);

  // Fitness calculation
  double calculate_fitness(const SeedInfo &seed);
  void update_fitness_scores();

  // Selection algorithms
  SeedInfo *select_weighted_random();
  SeedInfo *select_tournament();
  SeedInfo *select_roulette_wheel();
};

// Mutation engine with reproduction support
class MutationEngine {
public:
  explicit MutationEngine(uint32_t seed = 0);
  ~MutationEngine() = default;

  // Mutation interface
  std::vector<uint8_t> mutate(const std::vector<uint8_t> &input,
                              uint32_t depth = 1);
  std::vector<uint8_t>
  mutate_with_sequence(const std::vector<uint8_t> &input,
                       const std::vector<uint32_t> &sequence);

  // Reproduction support
  void set_seed(uint32_t seed);
  uint32_t get_seed() const { return current_seed_; }
  const std::vector<uint32_t> &get_mutation_sequence() const {
    return mutation_sequence_;
  }
  void clear_sequence() { mutation_sequence_.clear(); }

  // Dictionary support
  void set_dictionary(const std::vector<std::vector<uint8_t>> &dictionary);

  // Mutation strategies
  enum class MutationType {
    BIT_FLIP,
    BYTE_FLIP,
    ARITHMETIC,
    INTERESTING_VALUES,
    DICTIONARY,
    SPLICE,
    INSERT_DELETE,
    BLOCK_DUPLICATE,
    BLOCK_SHUFFLE
  };

private:
  uint32_t current_seed_;
  std::mt19937 gen_;
  std::vector<uint32_t> mutation_sequence_;
  std::vector<std::vector<uint8_t>> dictionary_;

  // Interesting values for mutations
  static const std::vector<uint8_t> interesting_8_;
  static const std::vector<uint16_t> interesting_16_;
  static const std::vector<uint32_t> interesting_32_;

  // Individual mutation methods
  void mutate_bit_flip(std::vector<uint8_t> &data, uint32_t param);
  void mutate_byte_flip(std::vector<uint8_t> &data, uint32_t param);
  void mutate_arithmetic(std::vector<uint8_t> &data, uint32_t param);
  void mutate_interesting_values(std::vector<uint8_t> &data, uint32_t param);
  void mutate_dictionary(std::vector<uint8_t> &data, uint32_t param);
  void mutate_splice(std::vector<uint8_t> &data, uint32_t param);
  void mutate_insert_delete(std::vector<uint8_t> &data, uint32_t param);
  void mutate_block_duplicate(std::vector<uint8_t> &data, uint32_t param);
  void mutate_block_shuffle(std::vector<uint8_t> &data, uint32_t param);

  // Helper functions
  uint32_t get_random_param();
  size_t get_random_position(size_t max_pos);
  size_t get_random_length(size_t max_len);
};

} // namespace fuzzer
