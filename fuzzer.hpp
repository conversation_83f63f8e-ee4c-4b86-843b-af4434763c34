#pragma once

#include <cstdint>
#include <string>
#include <vector>
#include <memory>
#include <functional>
#include <unordered_set>
#include <chrono>

namespace fuzzer {

// Forward declarations
class Corpus;
class Coverage;
class TargetResolver;
class ProcessInjector;

struct FuzzStats {
    uint64_t total_executions = 0;
    uint64_t crashes_found = 0;
    uint64_t unique_crashes = 0;
    uint64_t timeouts = 0;
    uint64_t new_coverage = 0;
    std::chrono::steady_clock::time_point start_time;

    void reset() {
        total_executions = 0;
        crashes_found = 0;
        unique_crashes = 0;
        timeouts = 0;
        new_coverage = 0;
        start_time = std::chrono::steady_clock::now();
    }

    double get_exec_per_sec() const {
        auto now = std::chrono::steady_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::seconds>(now - start_time);
        return duration.count() > 0 ? static_cast<double>(total_executions) / duration.count() : 0.0;
    }
};

struct FuzzConfig {
    std::string target_binary;
    std::string target_function;
    uint32_t target_pid = 0;
    std::string corpus_dir = "./corpus";
    std::string crash_dir = "./crashes";
    std::string output_dir = "./output";
    uint32_t max_input_size = 4096;
    uint32_t timeout_ms = 1000;
    uint32_t max_iterations = 1000000;
    bool verbose = false;
    bool minimize_crashes = true;
    bool track_coverage = true;
    uint32_t mutation_depth = 8;
    std::vector<std::string> seed_files;
};

enum class ExecutionResult {
    SUCCESS,
    CRASH,
    TIMEOUT,
    ERROR
};

struct ExecutionInfo {
    ExecutionResult result;
    uint32_t signal = 0;
    uint64_t execution_time_us = 0;
    bool new_coverage = false;
    std::string crash_hash;
};

class Fuzzer {
public:
    explicit Fuzzer(const FuzzConfig& config);
    ~Fuzzer();

    // Main fuzzing interface
    bool initialize();
    void run();
    void stop();

    // Target management
    bool attach_to_process(uint32_t pid);
    bool inject_into_process(uint32_t pid);
    bool set_target_function(const std::string& function_name);
    bool set_target_function(uintptr_t function_address);

    // Corpus management
    bool load_seeds(const std::vector<std::string>& seed_files);
    bool add_seed(const std::vector<uint8_t>& data);

    // Execution
    ExecutionInfo execute_input(const std::vector<uint8_t>& input);

    // Statistics
    const FuzzStats& get_stats() const { return stats_; }
    void print_stats() const;

private:
    FuzzConfig config_;
    FuzzStats stats_;

    std::unique_ptr<Corpus> corpus_;
    std::unique_ptr<Coverage> coverage_;
    std::unique_ptr<TargetResolver> target_resolver_;
    std::unique_ptr<ProcessInjector> injector_;

    // Target information
    uint32_t target_pid_ = 0;
    uintptr_t target_function_addr_ = 0;
    bool is_attached_ = false;
    bool should_stop_ = false;

    // Crash tracking
    std::unordered_set<std::string> unique_crash_hashes_;

    // Internal methods
    bool setup_directories();
    bool setup_signal_handlers();
    std::vector<uint8_t> mutate_input(const std::vector<uint8_t>& input);
    std::string calculate_crash_hash(const std::vector<uint8_t>& input, uint32_t signal);
    void save_crash(const std::vector<uint8_t>& input, const ExecutionInfo& exec_info);
    void save_interesting_input(const std::vector<uint8_t>& input);
    bool is_new_crash(const std::string& crash_hash);

    // Mutation strategies
    void mutate_bit_flip(std::vector<uint8_t>& data);
    void mutate_byte_flip(std::vector<uint8_t>& data);
    void mutate_arithmetic(std::vector<uint8_t>& data);
    void mutate_interesting_values(std::vector<uint8_t>& data);
    void mutate_dictionary(std::vector<uint8_t>& data);
    void mutate_splice(std::vector<uint8_t>& data);
    void mutate_insert_delete(std::vector<uint8_t>& data);

    // Signal handling
    static void signal_handler(int sig);
    static Fuzzer* instance_;
};

// Utility functions
std::vector<uint8_t> read_file(const std::string& filename);
bool write_file(const std::string& filename, const std::vector<uint8_t>& data);
std::string bytes_to_hex(const std::vector<uint8_t>& data);
std::vector<uint8_t> hex_to_bytes(const std::string& hex);

} // namespace fuzzer
