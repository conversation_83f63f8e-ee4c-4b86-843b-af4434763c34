#include "corpus.hpp"

#include <algorithm>
#include <cerrno>
#include <cstdint>
#include <cstring>
#include <fstream>
#include <iomanip>
#include <iostream>
#include <sstream>

#ifdef _WIN32
#include <direct.h>
#include <io.h>
#define mkdir(path, mode) _mkdir(path)
#else
#include <dirent.h>
#include <sys/stat.h>
#include <unistd.h>
#endif

namespace fuzzer {

// Interesting values for mutations
const std::vector<uint8_t> MutationEngine::interesting_8_ = {0x00, 0x01, 0x7F,
                                                             0x80, 0xFF};

const std::vector<uint16_t> MutationEngine::interesting_16_ = {
    0x0000, 0x0001, 0x007F, 0x0080, 0x00FF, 0x0100, 0x7FFF, 0x8000, 0xFFFF};

const std::vector<uint32_t> MutationEngine::interesting_32_ = {
    0x00000000, 0x00000001, 0x0000007F, 0x00000080, 0x000000FF,
    0x00000100, 0x00007FFF, 0x00008000, 0x0000FFFF, 0x00010000,
    0x7FFFFFFF, 0x80000000, 0xFFFFFFFF};

Corpus::Corpus(const std::string &corpus_dir)
    : corpus_dir_(corpus_dir), gen_(rd_()) {}

bool Corpus::initialize() {
  // Create corpus directory if it doesn't exist
  if (mkdir(corpus_dir_.c_str(), 0755) != 0 && errno != EEXIST) {
    std::cerr << "[-] Failed to create corpus directory: " << corpus_dir_
              << std::endl;
    return false;
  }

  // Load existing seeds
  return load_seeds_from_directory();
}

bool Corpus::load_seeds_from_directory() {
  std::cout << "[+] Loading seeds from " << corpus_dir_ << std::endl;

#ifdef _WIN32
  // Windows directory scanning would go here
  return true;
#else
  DIR *dir = opendir(corpus_dir_.c_str());
  if (!dir) {
    std::cout << "[+] No existing corpus directory found" << std::endl;
    return true; // Not an error if directory doesn't exist
  }

  struct dirent *entry;
  size_t loaded = 0;

  errno = 0;
  while ((entry = readdir(dir)) != nullptr) {
    if (entry->d_type == DT_REG) { // Regular file
      std::string filename = corpus_dir_ + "/" + entry->d_name;
      auto data = load_from_file(filename);
      if (!data.empty()) {
        if (add_seed(data, filename)) {
          loaded++;
        }
      }
    }
  }
  closedir(dir);
  std::cout << "[+] Loaded " << loaded << " seeds from corpus directory"
            << std::endl;
  return true;
#endif
}

bool Corpus::add_seed(const std::vector<uint8_t> &data,
                      const std::string &source) {
  if (data.empty()) {
    return false;
  }

  std::string hash = calculate_hash(data);
  if (is_duplicate(hash)) {
    return false; // Duplicate seed
  }

  auto seed = std::make_unique<SeedInfo>();
  seed->data = data;
  seed->hash = hash;
  seed->filename = generate_filename();

  // Save to file
  if (!save_to_file(seed->filename, data)) {
    return false;
  }

  // Add to corpus
  hash_to_index_[hash] = seeds_.size();
  seeds_.push_back(std::move(seed));

  return true;
}

SeedInfo *Corpus::select_seed() {
  if (seeds_.empty()) {
    return nullptr;
  }

  // Simple random selection for now
  std::uniform_int_distribution<size_t> dist(0, seeds_.size() - 1);
  size_t index = dist(gen_);

  return seeds_[index].get();
}

std::string Corpus::calculate_hash(const std::vector<uint8_t> &data) {
  // Simple hash calculation - would be enhanced with proper hashing
  std::stringstream ss;
  ss << std::hex;

  uint32_t hash = 0;
  for (uint8_t byte : data) {
    hash = hash * 31 + byte;
  }

  ss << hash << "_" << data.size();
  return ss.str();
}

bool Corpus::is_duplicate(const std::string &hash) {
  return hash_to_index_.find(hash) != hash_to_index_.end();
}

std::string Corpus::generate_filename() {
  static size_t counter = 0;
  std::stringstream ss;
  ss << corpus_dir_ << "/seed_" << std::setfill('0') << std::setw(8)
     << counter++;
  return ss.str();
}

bool Corpus::save_to_file(const std::string &filename,
                          const std::vector<uint8_t> &data) {
  std::ofstream file(filename, std::ios::binary);
  if (!file) {
    return false;
  }

  file.write(reinterpret_cast<const char *>(data.data()), data.size());
  return file.good();
}

std::vector<uint8_t> Corpus::load_from_file(const std::string &filename) {
  std::ifstream file(filename, std::ios::binary);
  if (!file) {
    return {};
  }

  file.seekg(0, std::ios::end);
  size_t size = file.tellg();
  file.seekg(0, std::ios::beg);

  std::vector<uint8_t> data(size);
  file.read(reinterpret_cast<char *>(data.data()), size);

  return data;
}

// MutationEngine implementation
MutationEngine::MutationEngine(uint32_t seed)
    : current_seed_(seed), gen_(seed) {}

std::vector<uint8_t> MutationEngine::mutate(const std::vector<uint8_t> &input,
                                            uint32_t depth) {
  std::vector<uint8_t> mutated = input;
  mutation_sequence_.clear();

  for (uint32_t i = 0; i < depth; ++i) {
    if (mutated.empty()) {
      mutated.push_back(0x41); // Add 'A' if empty
      continue;
    }

    // Select random mutation type
    std::uniform_int_distribution<int> type_dist(0, 8);
    MutationType type = static_cast<MutationType>(type_dist(gen_));

    uint32_t param = get_random_param();
    mutation_sequence_.push_back(static_cast<uint32_t>(type));
    mutation_sequence_.push_back(param);

    switch (type) {
    case MutationType::BIT_FLIP:
      mutate_bit_flip(mutated, param);
      break;
    case MutationType::BYTE_FLIP:
      mutate_byte_flip(mutated, param);
      break;
    case MutationType::ARITHMETIC:
      mutate_arithmetic(mutated, param);
      break;
    case MutationType::INTERESTING_VALUES:
      mutate_interesting_values(mutated, param);
      break;
    case MutationType::DICTIONARY:
      mutate_dictionary(mutated, param);
      break;
    case MutationType::SPLICE:
      mutate_splice(mutated, param);
      break;
    case MutationType::INSERT_DELETE:
      mutate_insert_delete(mutated, param);
      break;
    case MutationType::BLOCK_DUPLICATE:
      mutate_block_duplicate(mutated, param);
      break;
    case MutationType::BLOCK_SHUFFLE:
      mutate_block_shuffle(mutated, param);
      break;
    }
  }

  return mutated;
}

void MutationEngine::mutate_bit_flip(std::vector<uint8_t> &data,
                                     uint32_t param) {
  if (data.empty())
    return;

  size_t pos = param % data.size();
  uint8_t bit = param % 8;

  data[pos] ^= (1 << bit);
}

void MutationEngine::mutate_byte_flip(std::vector<uint8_t> &data,
                                      uint32_t param) {
  if (data.empty())
    return;

  size_t pos = param % data.size();
  data[pos] ^= 0xFF;
}

void MutationEngine::mutate_arithmetic(std::vector<uint8_t> &data,
                                       uint32_t param) {
  if (data.empty())
    return;

  size_t pos = param % data.size();
  int8_t delta = (param % 35) - 17; // -17 to +17

  data[pos] = static_cast<uint8_t>(data[pos] + delta);
}

void MutationEngine::mutate_interesting_values(std::vector<uint8_t> &data,
                                               uint32_t param) {
  if (data.empty())
    return;

  size_t pos = param % data.size();
  size_t value_idx = param % interesting_8_.size();

  data[pos] = interesting_8_[value_idx];
}

void MutationEngine::mutate_dictionary(std::vector<uint8_t> &data,
                                       uint32_t param) {
  if (dictionary_.empty() || data.empty())
    return;

  size_t dict_idx = param % dictionary_.size();
  const auto &dict_entry = dictionary_[dict_idx];

  if (dict_entry.empty())
    return;

  size_t pos = param % data.size();
  size_t copy_len = std::min(dict_entry.size(), data.size() - pos);

  std::copy(dict_entry.begin(), dict_entry.begin() + copy_len,
            data.begin() + pos);
}

void MutationEngine::mutate_insert_delete(std::vector<uint8_t> &data,
                                          uint32_t param) {
  if (param % 2 == 0) {
    // Insert
    if (data.size() < 4096) { // Max size limit
      size_t pos = param % (data.size() + 1);
      uint8_t value = param % 256;
      data.insert(data.begin() + pos, value);
    }
  } else {
    // Delete
    if (!data.empty()) {
      size_t pos = param % data.size();
      data.erase(data.begin() + pos);
    }
  }
}

void MutationEngine::mutate_splice(std::vector<uint8_t> &data, uint32_t param) {
  // Simple splice - copy part of data to another location
  if (data.size() < 2)
    return;

  size_t src_pos = param % data.size();
  size_t dst_pos = (param >> 8) % data.size();
  size_t len = std::min(static_cast<size_t>((param >> 16) % 8 + 1),
                        data.size() - src_pos);

  if (dst_pos + len <= data.size()) {
    std::copy(data.begin() + src_pos, data.begin() + src_pos + len,
              data.begin() + dst_pos);
  }
}

void MutationEngine::mutate_block_duplicate(std::vector<uint8_t> &data,
                                            uint32_t param) {
  if (data.empty() || data.size() >= 4096)
    return;

  size_t pos = param % data.size();
  size_t len =
      std::min(static_cast<size_t>((param >> 8) % 16 + 1), data.size() - pos);

  // Duplicate block
  std::vector<uint8_t> block(data.begin() + pos, data.begin() + pos + len);
  data.insert(data.end(), block.begin(), block.end());
}

void MutationEngine::mutate_block_shuffle(std::vector<uint8_t> &data,
                                          uint32_t param) {
  if (data.size() < 4)
    return;

  size_t block_size = (param % 8) + 1;
  if (block_size >= data.size())
    return;

  size_t pos = param % (data.size() - block_size);
  std::shuffle(data.begin() + pos, data.begin() + pos + block_size, gen_);
}

uint32_t MutationEngine::get_random_param() { return gen_(); }

void MutationEngine::set_seed(uint32_t seed) {
  current_seed_ = seed;
  gen_.seed(seed);
}

} // namespace fuzzer
