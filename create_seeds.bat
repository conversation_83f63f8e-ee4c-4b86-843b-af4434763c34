@echo off
REM Script to create initial seed files for fuzzing

echo Creating seed files for fuzzing...

REM Create corpus directory
if not exist corpus mkdir corpus

REM Seed 1: Simple string
echo Hello World > corpus\seed_hello.bin

REM Seed 2: Buffer overflow pattern
echo AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA > corpus\seed_overflow.bin

REM Seed 3: Format string pattern
echo %%x%%x%%x%%x%%x%%x%%x%%x > corpus\seed_format.bin

REM Create binary seeds using PowerShell
powershell -Command "[System.IO.File]::WriteAllBytes('corpus\seed_deadbeef.bin', [byte[]](0xDE,0xAD,0xBE,0xEF))"
powershell -Command "[System.IO.File]::WriteAllBytes('corpus\seed_intoverflow.bin', [byte[]](0xFF,0xFF,0xFF,0xFF))"
powershell -Command "[System.IO.File]::WriteAllBytes('corpus\seed_binary.bin', [byte[]](0x00,0x01,0x02,0x03,0x04,0x05,0x06,0x07,0x08,0x09,0x0A,0x0B,0x0C,0x0D,0x0E,0x0F))"
powershell -Command "[System.IO.File]::WriteAllBytes('corpus\seed_nulls.bin', [byte[]](0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00))"
powershell -Command "[System.IO.File]::WriteAllBytes('corpus\seed_high.bin', [byte[]](0xFF,0xFE,0xFD,0xFC,0xFB,0xFA,0xF9,0xF8))"
powershell -Command "[System.IO.File]::WriteAllBytes('corpus\seed_crash_trigger.bin', [byte[]](0x03,0xDE,0xAD,0xBE,0xEF,0x41,0x41,0x41,0x41))"

REM Create large seed
powershell -Command "$content = 'A' * 1024; [System.IO.File]::WriteAllText('corpus\seed_large.bin', $content)"

echo.
echo Created seed files:
dir corpus

echo.
echo Seed file descriptions:
echo   seed_hello.bin       - Simple ASCII string
echo   seed_deadbeef.bin    - Crash trigger pattern
echo   seed_overflow.bin    - Buffer overflow pattern
echo   seed_format.bin      - Format string pattern
echo   seed_intoverflow.bin - Integer overflow pattern
echo   seed_binary.bin      - Mixed binary data
echo   seed_large.bin       - Large input (1024 bytes)
echo   seed_nulls.bin       - Null bytes
echo   seed_high.bin        - High byte values
echo   seed_crash_trigger.bin - Specific crash trigger for test target

echo.
echo Usage examples:
echo   .\fuzzer.exe -p ^<PID^> -f fuzz_target -s corpus\seed_deadbeef.bin
echo   .\fuzzer.exe -p ^<PID^> -f fuzz_target -c corpus
