// Test target program with intentional vulnerabilities for fuzzing
// Compile with: g++ -m32 -g -o test_target test_target.cpp

#include <iostream>
#include <cstring>
#include <cstdlib>
#include <cstdint>
#include <csignal>

#ifdef _WIN32
    #include <windows.h>
    #include <process.h>
    #define getpid() _getpid()
    #define sleep(x) Sleep((x) * 1000)
#else
    #include <unistd.h>
    #include <signal.h>
#endif

// Vulnerable function 1: Buffer overflow
void vulnerable_strcpy(const char* input) {
    char buffer[64];
    strcpy(buffer, input); // Vulnerable: no bounds checking
    std::cout << "Copied: " << buffer << std::endl;
}

// Vulnerable function 2: Format string vulnerability
void vulnerable_printf(const char* input) {
    printf(input); // Vulnerable: user input as format string
    printf("\n");
}

// Vulnerable function 3: Integer overflow leading to buffer overflow
void vulnerable_malloc(size_t size) {
    if (size > 1024) {
        std::cout << "Size too large" << std::endl;
        return;
    }

    // Vulnerable: integer overflow can bypass check
    char* buffer = (char*)malloc(size * sizeof(char));
    if (!buffer) {
        std::cout << "Malloc failed" << std::endl;
        return;
    }

    // Fill buffer (potential overflow if size overflowed)
    for (size_t i = 0; i < size; ++i) {
        buffer[i] = 'A';
    }

    std::cout << "Allocated and filled " << size << " bytes" << std::endl;
    free(buffer);
}

// Vulnerable function 4: Stack-based buffer overflow with specific trigger
void vulnerable_stack_overflow(const uint8_t* data, size_t len) {
    char buffer[128];

    // Vulnerable: copies without bounds checking
    if (len > 0) {
        memcpy(buffer, data, len);
        buffer[len < 128 ? len : 127] = '\0';

        // Trigger crash on specific pattern
        if (len >= 4 &&
            data[0] == 0xDE &&
            data[1] == 0xAD &&
            data[2] == 0xBE &&
            data[3] == 0xEF) {

            // Intentional crash
            char* crash = nullptr;
            *crash = 0x42;
        }

        std::cout << "Processed " << len << " bytes" << std::endl;
    }
}

// Vulnerable function 5: Heap overflow
void vulnerable_heap_overflow(const uint8_t* data, size_t len) {
    if (len == 0) return;

    char* heap_buffer = (char*)malloc(64);
    if (!heap_buffer) return;

    // Vulnerable: no bounds checking
    memcpy(heap_buffer, data, len);

    // Use the buffer
    for (size_t i = 0; i < len && i < 64; ++i) {
        heap_buffer[i] ^= 0xAA;
    }

    std::cout << "Heap processed " << len << " bytes" << std::endl;
    free(heap_buffer);
}

// Function that can be targeted by fuzzer
extern "C" void fuzz_target(const uint8_t* data, size_t size) {
    if (!data || size == 0) {
        return;
    }

    std::cout << "[TARGET] Processing " << size << " bytes of input" << std::endl;

    // Route to different vulnerable functions based on first byte
    switch (data[0] % 5) {
        case 0:
            if (size > 1) {
                char* str = (char*)malloc(size);
                memcpy(str, data + 1, size - 1);
                str[size - 1] = '\0';
                vulnerable_strcpy(str);
                free(str);
            }
            break;

        case 1:
            if (size > 1) {
                char* str = (char*)malloc(size);
                memcpy(str, data + 1, size - 1);
                str[size - 1] = '\0';
                vulnerable_printf(str);
                free(str);
            }
            break;

        case 2:
            if (size >= sizeof(size_t)) {
                size_t malloc_size = *(size_t*)(data + 1);
                vulnerable_malloc(malloc_size);
            }
            break;

        case 3:
            vulnerable_stack_overflow(data + 1, size - 1);
            break;

        case 4:
            vulnerable_heap_overflow(data + 1, size - 1);
            break;
    }
}

void signal_handler(int sig) {
    std::cout << "\n[TARGET] Received signal " << sig << ", exiting..." << std::endl;
    exit(0);
}

void print_target_info() {
    std::cout << "=== Test Target Program ===" << std::endl;
    std::cout << "PID: " << getpid() << std::endl;
    std::cout << "fuzz_target address: " << (void*)fuzz_target << std::endl;
    std::cout << "vulnerable_strcpy address: " << (void*)vulnerable_strcpy << std::endl;
    std::cout << "vulnerable_printf address: " << (void*)vulnerable_printf << std::endl;
    std::cout << "vulnerable_malloc address: " << (void*)vulnerable_malloc << std::endl;
    std::cout << "vulnerable_stack_overflow address: " << (void*)vulnerable_stack_overflow << std::endl;
    std::cout << "vulnerable_heap_overflow address: " << (void*)vulnerable_heap_overflow << std::endl;
    std::cout << "===========================" << std::endl;
}

int main(int argc, char* argv[]) {
    // Setup signal handlers
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);

    print_target_info();

    if (argc > 1) {
        // Test mode - run with provided input
        std::string input = argv[1];
        std::cout << "[TARGET] Test mode with input: " << input << std::endl;
        fuzz_target(reinterpret_cast<const uint8_t*>(input.c_str()), input.length());
        return 0;
    }

    std::cout << "[TARGET] Waiting for fuzzer attachment..." << std::endl;
    std::cout << "[TARGET] Use: ./fuzzer -p " << getpid() << " -f fuzz_target" << std::endl;
    std::cout << "[TARGET] Or test with: " << argv[0] << " \"test_input\"" << std::endl;

    // Keep the process running for fuzzer attachment
    while (true) {
        sleep(1);
    }

    return 0;
}
