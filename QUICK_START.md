# Quick Start Guide - Linux 32-bit Fuzzer

## Prerequisites

### For Linux (Primary Target)
```bash
# Ubuntu/Debian
sudo apt-get update
sudo apt-get install build-essential gcc-multilib g++-multilib libc6-dev-i386

# CentOS/RHEL
sudo yum groupinstall "Development Tools"
sudo yum install glibc-devel.i686 libgcc.i686
```

### For Windows (Development/Testing)
- Install MinGW-w64 or similar GCC compiler
- Or use WSL (Windows Subsystem for Linux)

## Quick Build and Test

### 1. Clone/Download the Source
```bash
# If using git
git clone <repository>
cd fuzzer

# Or extract from archive
tar -xzf fuzzer.tar.gz
cd fuzzer
```

### 2. Build Everything
```bash
# Linux
make

# Windows (if using the provided batch file)
.\build_and_test.bat
```

### 3. Create Seed Files
```bash
# Linux
./create_seeds.sh

# Windows
.\create_seeds.bat
```

### 4. Test with Provided Target

#### Terminal 1: Start Test Target
```bash
# Linux
./test_target

# Windows (if compiled)
.\test_target.exe
```

Note the output:
```
=== Test Target Program ===
PID: 12345
fuzz_target address: 0x08048abc
...
```

#### Terminal 2: Run Fuzzer
```bash
# Linux - Basic fuzzing
./fuzzer -p 12345 -f fuzz_target

# Linux - With custom corpus
./fuzzer -p 12345 -f fuzz_target -c corpus -i 100000

# Linux - Target specific address
./fuzzer -p 12345 -a 0x08048abc -v
```

## Common Usage Patterns

### 1. Fuzz Running Process
```bash
# Find target process
ps aux | grep target_program

# Attach and fuzz
./fuzzer -p <PID> -f vulnerable_function
```

### 2. Fuzz with Custom Seeds
```bash
# Create custom seed
echo -ne '\xDE\xAD\xBE\xEF\x41\x41\x41\x41' > my_seed.bin

# Use custom seed
./fuzzer -p <PID> -f target_func -s my_seed.bin
```

### 3. High-Performance Fuzzing
```bash
# Disable coverage for speed
./fuzzer -p <PID> -f target_func --no-coverage -i 10000000
```

### 4. Verbose Debugging
```bash
# Enable verbose output
./fuzzer -p <PID> -f target_func -v
```

## Expected Output

### Successful Startup
```
 ███████╗██╗   ██╗███████╗███████╗███████╗██████╗ 
 ██╔════╝██║   ██║╚══███╔╝╚══███╔╝██╔════╝██╔══██╗
 █████╗  ██║   ██║  ███╔╝   ███╔╝ █████╗  ██████╔╝
 ██╔══╝  ██║   ██║ ███╔╝   ███╔╝  ██╔══╝  ██╔══██╗
 ██║     ╚██████╔╝███████╗███████╗███████╗██║  ██║
 ╚═╝      ╚═════╝ ╚══════╝╚══════╝╚══════╝╚═╝  ╚═╝
                                                   
 Linux 32-bit Process Fuzzer v1.0

[+] Initializing fuzzer...
[+] Successfully attached to process 12345
[+] Target function set to 0x8048abc
[+] Starting fuzzing campaign...

=== Fuzzing Statistics ===
Executions: 1000
Exec/sec: 1250.50
Crashes: 2 (unique: 1)
Timeouts: 0
New coverage: 15
=========================
```

### Crash Discovery
```
[!] New crash found! Hash: crash_signal11_64
[+] Crash saved to: crashes/crash_signal11_64.bin
```

## Troubleshooting

### Permission Issues
```bash
# Enable ptrace for non-root users
echo 0 | sudo tee /proc/sys/kernel/yama/ptrace_scope

# Or run as root (not recommended for production)
sudo ./fuzzer -p <PID> -f target_func
```

### Function Not Found
```bash
# Check available symbols
nm -D /path/to/binary | grep function_name
objdump -t /path/to/binary | grep function_name

# Use address instead
./fuzzer -p <PID> -a 0x08048000
```

### Build Errors
```bash
# Install 32-bit libraries
sudo apt-get install libc6-dev-i386 gcc-multilib

# Check compiler
g++ --version

# Clean and rebuild
make clean
make
```

### Process Not Found
```bash
# Check if process is running
ps aux | grep process_name

# Check PID
pgrep process_name
```

## File Locations

After running, you'll find:

```
corpus/          # Seed files and interesting inputs
├── seed_hello.bin
├── seed_deadbeef.bin
└── ...

crashes/         # Crash-inducing inputs
├── crash_signal11_64.bin
└── ...

output/          # General output and logs
```

## Advanced Configuration

### Custom Configuration
Edit `fuzzer_config.json` for advanced settings:
- Mutation strategies
- Timeout values
- Coverage options
- Target functions

### Environment Variables
```bash
# Increase verbosity
export FUZZ_VERBOSE=1

# Custom corpus directory
export FUZZ_CORPUS_DIR=/path/to/corpus
```

## Performance Tips

1. **Disable coverage** for maximum speed: `--no-coverage`
2. **Use SSD storage** for corpus and crash directories
3. **Increase iterations** for longer campaigns: `-i 10000000`
4. **Use multiple instances** on different cores
5. **Monitor system resources** to avoid overload

## Safety Considerations

1. **Test on isolated systems** - fuzzing can crash targets
2. **Backup important data** before fuzzing system components
3. **Monitor disk space** - corpus can grow large
4. **Set resource limits** to prevent system overload
5. **Use appropriate permissions** - avoid running as root when possible

## Getting Help

1. **Check the logs** in output directory
2. **Use verbose mode** `-v` for detailed output
3. **Review build errors** in `build_errors.txt`
4. **Check system requirements** and dependencies
5. **Verify target process** is running and accessible

## Next Steps

Once you have the basic fuzzing working:

1. **Analyze crashes** found in the crashes directory
2. **Expand corpus** with domain-specific inputs
3. **Target multiple functions** in the same process
4. **Implement custom mutation strategies**
5. **Set up continuous fuzzing** for long-term testing

For more detailed information, see `README.md` and `IMPLEMENTATION_SUMMARY.md`.
