#include "corpus.hpp"
#include "library_injector.hpp"
#include <getopt.h>
#include <iomanip>
#include <iostream>
#include <signal.h>
#include <string>
#include <vector>

using namespace fuzzer;

void print_banner() {
  std::cout << R"(
 ╔═══════════════════════════════════════════════════════════════╗
 ║                    Library-Based Fuzzer                      ║
 ║                   Advanced Process Fuzzing                   ║
 ║                      via Library Injection                   ║
 ╚═══════════════════════════════════════════════════════════════╝
)" << '\n';
}

void print_usage(const char *program_name) {
  std::cout
      << "Usage: " << program_name << " [options]\n"
      << "\nOptions:\n"
      << "  -p, --pid PID              Target process PID\n"
      << "  -f, --function NAME        Target function name\n"
      << "  -a, --address ADDR         Target function address (hex)\n"
      << "  -l, --library PATH         Agent library path (default: "
         "./libfuzz_agent.so)\n"
      << "  -c, --corpus DIR           Corpus directory (default: ./corpus)\n"
      << "  -o, --output DIR           Output directory (default: ./output)\n"
      << "  -s, --seed FILE            Seed file(s) to load\n"
      << "  -i, --iterations NUM       Maximum iterations (default: 1000000)\n"
      << "  -t, --timeout MS           Timeout in milliseconds (default: "
         "1000)\n"
      << "  -v, --verbose              Verbose output\n"
      << "  --no-coverage              Disable coverage tracking\n"
      << "  --help                     Show this help message\n"
      << "\nExamples:\n"
      << "  " << program_name << " -p 1234 -f fuzz_target\n"
      << "  " << program_name << " -p 1234 -a 0x08048000 -c ./seeds\n"
      << "  " << program_name
      << " -p 1234 -f vulnerable_func -s seed1.bin -s seed2.bin\n"
      << std::endl;
}

bool parse_arguments(int argc, char *argv[], FuzzConfig &config,
                     std::string &library_path) {
  static struct option long_options[] = {
      {"pid", required_argument, 0, 'p'},
      {"function", required_argument, 0, 'f'},
      {"address", required_argument, 0, 'a'},
      {"library", required_argument, 0, 'l'},
      {"corpus", required_argument, 0, 'c'},
      {"output", required_argument, 0, 'o'},
      {"seed", required_argument, 0, 's'},
      {"iterations", required_argument, 0, 'i'},
      {"timeout", required_argument, 0, 't'},
      {"verbose", no_argument, 0, 'v'},
      {"no-coverage", no_argument, 0, 1001},
      {"help", no_argument, 0, 1000},
      {0, 0, 0, 0}};

  int option_index = 0;
  int c;

  while ((c = getopt_long(argc, argv, "p:f:a:l:c:o:s:i:t:v", long_options,
                          &option_index)) != -1) {
    switch (c) {
    case 'p':
      config.target_pid = std::stoul(optarg);
      break;
    case 'f':
      config.target_function = optarg;
      break;
    case 'a':
      // Parse hex address
      config.target_function = optarg; // Store as string for now
      break;
    case 'l':
      library_path = optarg;
      break;
    case 'c':
      config.corpus_dir = optarg;
      break;
    case 'o':
      config.output_dir = optarg;
      break;
    case 's':
      config.seed_files.push_back(optarg);
      break;
    case 'i':
      config.max_iterations = std::stoul(optarg);
      break;
    case 't':
      config.timeout_ms = std::stoul(optarg);
      break;
    case 'v':
      config.verbose = true;
      break;
    case 1001:
      config.track_coverage = false;
      break;
    case 1000:
      print_usage(argv[0]);
      return false;
    case '?':
      return false;
    default:
      return false;
    }
  }

  return true;
}

bool validate_config(const FuzzConfig &config) {
  if (config.target_pid == 0) {
    std::cerr << "[-] Target PID is required" << '\n';
    return false;
  }

  if (config.target_function.empty()) {
    std::cerr << "[-] Target function name or address is required" << '\n';
    return false;
  }

  return true;
}

void print_config(const FuzzConfig &config, const std::string &library_path) {
  std::cout << "\n=== Fuzzer Configuration ===" << '\n';
  std::cout << "Target PID: " << config.target_pid << '\n';
  std::cout << "Target Function: " << config.target_function << '\n';
  std::cout << "Agent Library: " << library_path << '\n';
  std::cout << "Corpus Directory: " << config.corpus_dir << '\n';
  std::cout << "Output Directory: " << config.output_dir << '\n';
  std::cout << "Max Iterations: " << config.max_iterations << '\n';
  std::cout << "Timeout: " << config.timeout_ms << "ms" << '\n';
  std::cout << "Coverage Tracking: "
            << (config.track_coverage ? "Enabled" : "Disabled") << '\n';
  std::cout << "Verbose: " << (config.verbose ? "Enabled" : "Disabled") << '\n';

  if (!config.seed_files.empty()) {
    std::cout << "Seed Files: ";
    for (const auto &seed : config.seed_files) {
      std::cout << seed << " ";
    }
    std::cout << '\n';
  }

  std::cout << "============================" << '\n';
}

// Global fuzzer instance for signal handling
LibraryFuzzer *g_fuzzer = nullptr;

void signal_handler(int sig) {
  std::cout << "\n[+] Received signal " << sig << ", stopping fuzzer..."
            << '\n';
  if (g_fuzzer) {
    g_fuzzer->stop();
  }
}

void setup_signal_handlers() {
  signal(SIGINT, signal_handler);
  signal(SIGTERM, signal_handler);
}

int main(int argc, char *argv[]) {
  print_banner();

  FuzzConfig config;
  std::string library_path = "./libfuzz_agent.so";

  if (!parse_arguments(argc, argv, config, library_path)) {
    print_usage(argv[0]);
    return 1;
  }

  if (!validate_config(config)) {
    return 1;
  }

  print_config(config, library_path);
  setup_signal_handlers();

  // Create library injector
  LibraryInjector injector;

  std::cout << "\n[+] Attaching to process " << config.target_pid << "..."
            << '\n';
  if (!injector.attach_to_process(config.target_pid)) {
    std::cerr << "[-] Failed to attach to process " << config.target_pid
              << '\n';
    return 1;
  }

  std::cout << "[+] Injecting agent library..." << '\n';
  if (!injector.inject_agent_library(library_path)) {
    std::cerr << "[-] Failed to inject agent library" << '\n';
    return 1;
  }

  // Set target function
  std::cout << "[+] Setting target function..." << '\n';
  bool function_set = false;

  // Check if it's a hex address
  if (config.target_function.substr(0, 2) == "0x" ||
      config.target_function.substr(0, 2) == "0X") {
    uintptr_t address = std::stoull(config.target_function, nullptr, 16);
    function_set = injector.set_target_function(address);
  } else {
    function_set = injector.set_target_function(config.target_function);
  }

  if (!function_set) {
    std::cerr << "[-] Failed to set target function: " << config.target_function
              << '\n';
    return 1;
  }

  // Create corpus
  Corpus corpus(config.corpus_dir);
  if (!corpus.initialize()) {
    std::cerr << "[-] Failed to initialize corpus" << '\n';
    return 1;
  }

  // Load seed files
  /*for (const auto &seed_file : config.seed_files) {
    std::cout << "[+] Loading seed file: " << seed_file << '\n';
    if (!corpus.load_seed_file(seed_file)) {
      std::cerr << "[-] Failed to load seed file: " << seed_file << '\n';
    }
  }

   if (corpus.get_seed_count() == 0) {
    std::cout << "[+] No seeds loaded, creating default seed..." << '\n';
    std::vector<uint8_t> default_seed = {'H', 'e', 'l', 'l', 'o'};
    corpus.add_seed(default_seed, "default");
  } std::cout << "[+] Corpus loaded with " << corpus.get_seed_count() << "
  seeds"
            << '\n';
            */
  if (!corpus.load_seeds_from_directory()) {
    std::cerr << "[-] Failed to load seeds from directory" << '\n';
    return 1;
  }

  // Start fuzzing
  std::cout << "\n[+] Starting fuzzing campaign..." << '\n';

  uint64_t total_executions = 0;
  uint64_t crashes_found = 0;
  uint64_t timeouts = 0;
  uint64_t new_coverage = 0;

  auto start_time = std::chrono::steady_clock::now();

  while (total_executions < config.max_iterations) {
    // Select a seed
    auto *seed = corpus.select_seed();
    if (!seed) {
      std::cerr << "[-] No seeds available" << '\n';
      break;
    }

    // Simple mutation (could be enhanced)
    std::vector<uint8_t> mutated_input = seed->data;
    if (!mutated_input.empty() && (rand() % 10) == 0) {
      size_t pos = rand() % mutated_input.size();
      mutated_input[pos] = rand() % 256;
    }

    // Execute input
    AgentExecutionResult result =
        injector.execute_input(mutated_input, config.timeout_ms);
    total_executions++;

    // Handle result
    switch (result.result) {
    case AgentResponse::SUCCESS:
      if (config.track_coverage) {
        // Check for new coverage (simplified)
        new_coverage++;
      }
      break;

    case AgentResponse::CRASH:
      crashes_found++;
      std::cout << "[!] Crash found! Signal: " << result.signal_number
                << ", Execution: " << total_executions << '\n';
      break;

    case AgentResponse::TIMEOUT:
      timeouts++;
      break;

    default:
      break;
    }

    // Print periodic statistics
    if (total_executions % 1000 == 0) {
      auto current_time = std::chrono::steady_clock::now();
      auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(
                         current_time - start_time)
                         .count();
      double exec_per_sec =
          elapsed > 0 ? static_cast<double>(total_executions) / elapsed : 0.0;

      std::cout << "\n=== Statistics ===" << '\n';
      std::cout << "Executions: " << total_executions << '\n';
      std::cout << "Exec/sec: " << std::fixed << std::setprecision(2)
                << exec_per_sec << '\n';
      std::cout << "Crashes: " << crashes_found << '\n';
      std::cout << "Timeouts: " << timeouts << '\n';
      std::cout << "New coverage: " << new_coverage << '\n';
      std::cout << "==================" << '\n';
    }
  }

  std::cout << "\n[+] Fuzzing campaign completed" << '\n';
  std::cout << "Total executions: " << total_executions << '\n';
  std::cout << "Crashes found: " << crashes_found << '\n';
  std::cout << "Timeouts: " << timeouts << '\n';

  // Shutdown agent
  std::cout << "[+] Shutting down agent..." << '\n';
  injector.shutdown_agent();

  return 0;
}
