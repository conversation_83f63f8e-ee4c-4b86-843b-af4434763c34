{"fuzzer_config": {"target": {"pid": 0, "binary_path": "", "function_name": "fuzz_target", "function_address": "0x0"}, "directories": {"corpus_dir": "./corpus", "crash_dir": "./crashes", "output_dir": "./output"}, "fuzzing": {"max_iterations": 1000000, "timeout_ms": 1000, "max_input_size": 4096, "mutation_depth": 8}, "features": {"track_coverage": true, "minimize_crashes": true, "verbose": false}, "seeds": ["corpus/seed_hello.bin", "corpus/seed_deadbeef.bin", "corpus/seed_overflow.bin"]}, "examples": {"basic_fuzzing": {"description": "Basic function fuzzing", "command": "./fuzzer -p 1234 -f vulnerable_function"}, "address_fuzzing": {"description": "Fuzz specific address", "command": "./fuzzer -p 1234 -a 0x08048000"}, "corpus_fuzzing": {"description": "Fuzz with custom corpus", "command": "./fuzzer -p 1234 -f main -c ./my_corpus -s seed1.bin"}, "high_performance": {"description": "High performance fuzzing", "command": "./fuzzer -p 1234 -f target --no-coverage -i 10000000"}}, "target_functions": {"common_vulnerable": ["strcpy", "strcat", "sprintf", "gets", "scanf", "memcpy", "memmove"], "custom_targets": ["fuzz_target", "vulnerable_function", "parse_input", "process_data"]}, "mutation_strategies": {"bit_flip": {"enabled": true, "weight": 10}, "byte_flip": {"enabled": true, "weight": 10}, "arithmetic": {"enabled": true, "weight": 15}, "interesting_values": {"enabled": true, "weight": 20}, "dictionary": {"enabled": true, "weight": 15}, "splice": {"enabled": true, "weight": 10}, "insert_delete": {"enabled": true, "weight": 10}, "block_operations": {"enabled": true, "weight": 10}}, "interesting_values": {"8_bit": [0, 1, 127, 128, 255], "16_bit": [0, 1, 127, 128, 255, 256, 32767, 32768, 65535], "32_bit": [0, 1, 127, 128, 255, 256, 32767, 32768, 65535, 65536, 2147483647, 2147483648, 4294967295]}, "crash_patterns": {"stack_overflow": {"pattern": "AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA", "description": "Basic stack buffer overflow"}, "format_string": {"pattern": "%x%x%x%x%x%x%x%x", "description": "Format string vulnerability"}, "integer_overflow": {"pattern": "\\xFF\\xFF\\xFF\\xFF", "description": "Integer overflow trigger"}, "deadbeef_trigger": {"pattern": "\\xDE\\xAD\\xBE\\xEF", "description": "Specific crash trigger for test target"}}}