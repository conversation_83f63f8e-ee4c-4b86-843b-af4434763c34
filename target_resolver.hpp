#pragma once

#include <cstdint>
#include <string>
#include <vector>
#include <memory>

namespace fuzzer {

// Forward declarations
class ProcessInjector;
class ELF32Parser;

struct FunctionInfo {
    std::string name;
    uintptr_t address;
    size_t size;
    std::string module;
    bool is_exported;
    bool is_dynamic;
};

struct ModuleInfo {
    std::string name;
    std::string path;
    uintptr_t base_address;
    size_t size;
    std::vector<FunctionInfo> functions;
};

class TargetResolver {
public:
    explicit TargetResolver(ProcessInjector* injector);
    ~TargetResolver() = default;
    
    // Target resolution
    bool resolve_function(const std::string& function_name, uintptr_t& address);
    bool resolve_function_in_module(const std::string& function_name, 
                                   const std::string& module_name, 
                                   uintptr_t& address);
    bool resolve_address_to_function(uintptr_t address, FunctionInfo& function);
    
    // Module analysis
    bool analyze_process_modules();
    bool analyze_module(const std::string& module_name);
    std::vector<ModuleInfo> get_loaded_modules() const { return modules_; }
    
    // Function discovery
    std::vector<FunctionInfo> find_functions_by_pattern(const std::string& pattern);
    std::vector<FunctionInfo> get_exported_functions(const std::string& module_name);
    std::vector<FunctionInfo> get_all_functions() const;
    
    // Symbol resolution
    bool resolve_symbol(const std::string& symbol_name, uintptr_t& address);
    bool resolve_symbol_in_module(const std::string& symbol_name,
                                 const std::string& module_name,
                                 uintptr_t& address);
    
    // Address validation
    bool is_valid_function_address(uintptr_t address);
    bool is_address_in_module(uintptr_t address, const std::string& module_name);
    
    // Function analysis
    bool get_function_bounds(uintptr_t address, uintptr_t& start, uintptr_t& end);
    size_t estimate_function_size(uintptr_t function_address);
    
    // Dynamic symbol resolution
    bool resolve_dynamic_symbol(const std::string& symbol_name, uintptr_t& address);
    bool hook_dynamic_function(const std::string& function_name, uintptr_t hook_address);
    
    // PLT/GOT analysis
    bool analyze_plt_got();
    std::vector<std::pair<std::string, uintptr_t>> get_plt_entries();
    std::vector<std::pair<std::string, uintptr_t>> get_got_entries();
    
private:
    ProcessInjector* injector_;
    std::vector<ModuleInfo> modules_;
    
    // Internal helpers
    bool parse_proc_maps();
    bool analyze_elf_module(const std::string& module_path, uintptr_t base_address);
    bool scan_for_functions(uintptr_t start_address, uintptr_t end_address);
    
    // Function signature detection
    bool is_function_prologue(uintptr_t address);
    bool is_function_epilogue(uintptr_t address);
    std::vector<uintptr_t> find_function_prologues(uintptr_t start, uintptr_t end);
    
    // Symbol table parsing
    bool parse_symbol_table(const std::string& module_path, 
                           uintptr_t base_address,
                           std::vector<FunctionInfo>& functions);
    bool parse_dynamic_symbols(const std::string& module_path,
                              uintptr_t base_address,
                              std::vector<FunctionInfo>& functions);
    
    // Pattern matching
    bool match_pattern(const std::string& text, const std::string& pattern);
    
    // Address space analysis
    struct MemoryRegion {
        uintptr_t start;
        uintptr_t end;
        std::string permissions;
        std::string path;
    };
    
    std::vector<MemoryRegion> memory_regions_;
    bool parse_memory_regions();
    bool is_executable_region(uintptr_t address);
    
    // Function heuristics for x86-32
    static const std::vector<uint8_t> FUNCTION_PROLOGUES[];
    static const std::vector<uint8_t> FUNCTION_EPILOGUES[];
    
    bool matches_prologue(const uint8_t* bytes, size_t length);
    bool matches_epilogue(const uint8_t* bytes, size_t length);
    
    // PLT/GOT structures
    struct PLTEntry {
        std::string symbol_name;
        uintptr_t plt_address;
        uintptr_t got_address;
    };
    
    std::vector<PLTEntry> plt_entries_;
    bool parse_plt_section(uintptr_t plt_address, size_t plt_size);
    bool parse_got_section(uintptr_t got_address, size_t got_size);
};

// Utility functions for target resolution
class SymbolResolver {
public:
    static bool resolve_libc_function(const std::string& function_name, uintptr_t& address);
    static bool resolve_kernel_symbol(const std::string& symbol_name, uintptr_t& address);
    static std::vector<std::string> get_common_target_functions();
    
    // Common vulnerable function patterns
    static const std::vector<std::string> VULNERABLE_FUNCTIONS;
    static const std::vector<std::string> STRING_FUNCTIONS;
    static const std::vector<std::string> MEMORY_FUNCTIONS;
    static const std::vector<std::string> FILE_FUNCTIONS;
    static const std::vector<std::string> NETWORK_FUNCTIONS;
};

} // namespace fuzzer
