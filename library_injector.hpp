#pragma once

#include "corpus.hpp"
#include "fuzz_agent.hpp"
#include "injector.hpp"
#include "shared.hpp"
#include <chrono>
#include <memory>
#include <set>
#include <string>

namespace fuzzer {

// Forward declarations
class ProcessInjector;

// Library injection and communication class
class LibraryInjector {
public:
  LibraryInjector();
  ~LibraryInjector();

  // Process management
  bool attach_to_process(uint32_t pid);
  bool detach_from_process();
  bool is_attached() const { return attached_pid_ != 0; }
  uint32_t get_attached_pid() const { return attached_pid_; }

  // Library injection
  bool inject_agent_library(const std::string &library_path = "");
  bool is_agent_injected() const { return agent_injected_; }

  // Communication with injected agent
  bool ping_agent(uint32_t timeout_ms = 1000);
  bool set_target_function(const std::string &function_name);
  bool set_target_function(uintptr_t function_address);

  // Fuzzing execution
  AgentExecutionResult execute_input(const std::vector<uint8_t> &input,
                                     uint32_t timeout_ms = 1000);

  // Coverage and statistics
  CoverageInfo get_coverage();
  void reset_coverage();

  struct AgentStats {
    uint64_t total_executions;
    uint64_t total_crashes;
    uint64_t total_timeouts;
    uint32_t coverage_count;
  };

  AgentStats get_agent_stats();

  // Agent control
  bool shutdown_agent();

private:
  uint32_t attached_pid_;
  bool agent_injected_;
  std::string shm_name_;

  // Shared memory communication
  SharedMemoryLayout *shared_memory_;
  int shm_fd_;

  // Process injector for library injection
  std::unique_ptr<ProcessInjector> process_injector_;

  // Internal methods
  bool create_shared_memory();
  void cleanup_shared_memory();
  bool wait_for_response(uint32_t timeout_ms);
  bool send_command(AgentCommand command, uint32_t timeout_ms = 1000);

  // Platform-specific injection
  bool inject_library_windows(uint32_t pid, const std::string &library_path);
  bool inject_library_linux(uint32_t pid, const std::string &library_path);

  // Default library paths
  std::string get_default_library_path();
  bool library_exists(const std::string &path);
};

// Utility functions for library injection
namespace injection_utils {
// Process utilities
bool is_process_running(uint32_t pid);
std::string get_process_name(uint32_t pid);
std::string get_process_path(uint32_t pid);

// Library utilities
std::string find_library_in_path(const std::string &library_name);
bool is_library_loaded(uint32_t pid, const std::string &library_name);
std::vector<std::string> get_loaded_libraries(uint32_t pid);

// Architecture detection
enum class ProcessArchitecture { UNKNOWN, X86_32, X86_64, ARM32, ARM64 };

ProcessArchitecture get_process_architecture(uint32_t pid);
bool is_architecture_compatible(ProcessArchitecture target_arch);

// Permission checking
bool can_inject_into_process(uint32_t pid);
bool has_required_privileges();

// Error handling
std::string get_last_injection_error();
void set_injection_error(const std::string &error);
} // namespace injection_utils

// High-level fuzzer interface using library injection
class LibraryFuzzer {
public:
  explicit LibraryFuzzer(const FuzzConfig &config);
  ~LibraryFuzzer();

  // Initialization
  bool initialize();
  void shutdown();

  // Target management
  bool attach_to_process(uint32_t pid);
  bool set_target_function(const std::string &function_name);
  bool set_target_function(uintptr_t function_address);

  // Fuzzing execution
  ExecutionInfo execute_input(const std::vector<uint8_t> &input);

  // Main fuzzing loop
  void run();
  void stop();

  // Statistics
  const FuzzStats &get_stats() const { return stats_; }
  void print_stats() const;

private:
  FuzzConfig config_;
  FuzzStats stats_;

  std::unique_ptr<LibraryInjector> injector_;
  std::unique_ptr<Corpus> corpus_;

  // Target information
  uint32_t target_pid_;
  std::string target_function_name_;
  uintptr_t target_function_address_;

  // Control flags
  bool initialized_;
  bool should_stop_;

  // Crash tracking
  std::set<std::string> unique_crash_hashes_;

  // Internal methods
  std::vector<uint8_t> mutate_input(const std::vector<uint8_t> &input);
  std::string calculate_crash_hash(const std::vector<uint8_t> &input,
                                   uint32_t signal);
  bool is_new_crash(const std::string &crash_hash);
  void save_crash(const std::vector<uint8_t> &input,
                  const ExecutionInfo &exec_info);
  void save_interesting_input(const std::vector<uint8_t> &input);

  // Statistics helpers
  void update_stats_from_agent();
  void print_periodic_stats();

  // Signal handling
  static void signal_handler(int sig);
  static LibraryFuzzer *instance_;
};

} // namespace fuzzer
