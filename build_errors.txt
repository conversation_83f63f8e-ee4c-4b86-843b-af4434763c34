injector.cpp: In member function 'bool fuzzer::ProcessInjector::attach(fuzzer::process_id_t)':
injector.cpp:32:43: warning: unused parameter 'pid' [-Wunused-parameter]
   32 | bool ProcessInjector::attach(process_id_t pid) {
      |                              ~~~~~~~~~~~~~^~~
injector.cpp: In member function 'bool fuzzer::ProcessInjector::read_memory(uintptr_t, void*, size_t)':
injector.cpp:70:45: warning: unused parameter 'address' [-Wunused-parameter]
   70 | bool ProcessInjector::read_memory(uintptr_t address, void *buffer,
      |                                   ~~~~~~~~~~^~~~~~~
injector.cpp:70:60: warning: unused parameter 'buffer' [-Wunused-parameter]
   70 | bool ProcessInjector::read_memory(uintptr_t address, void *buffer,
      |                                                      ~~~~~~^~~~~~
injector.cpp:71:42: warning: unused parameter 'size' [-Wunused-parameter]
   71 |                                   size_t size) {
      |                                   ~~~~~~~^~~~
injector.cpp: In member function 'bool fuzzer::ProcessInjector::write_memory(uintptr_t, const void*, size_t)':
injector.cpp:95:46: warning: unused parameter 'address' [-Wunused-parameter]
   95 | bool ProcessInjector::write_memory(uintptr_t address, const void *data,
      |                                    ~~~~~~~~~~^~~~~~~
injector.cpp:95:67: warning: unused parameter 'data' [-Wunused-parameter]
   95 | bool ProcessInjector::write_memory(uintptr_t address, const void *data,
      |                                                       ~~~~~~~~~~~~^~~~
injector.cpp:96:43: warning: unused parameter 'size' [-Wunused-parameter]
   96 |                                    size_t size) {
      |                                    ~~~~~~~^~~~
injector.cpp: In member function 'bool fuzzer::ProcessInjector::set_breakpoint(uintptr_t)':
injector.cpp:131:48: warning: unused parameter 'address' [-Wunused-parameter]
  131 | bool ProcessInjector::set_breakpoint(uintptr_t address) {
      |                                      ~~~~~~~~~~^~~~~~~
injector.cpp: In member function 'bool fuzzer::ProcessInjector::remove_breakpoint(uintptr_t)':
injector.cpp:153:51: warning: unused parameter 'address' [-Wunused-parameter]
  153 | bool ProcessInjector::remove_breakpoint(uintptr_t address) {
      |                                         ~~~~~~~~~~^~~~~~~
injector.cpp: In member function 'bool fuzzer::ProcessInjector::wait_for_signal(int&, int&, uint32_t)':
injector.cpp:187:44: warning: unused parameter 'signal' [-Wunused-parameter]
  187 | bool ProcessInjector::wait_for_signal(int &signal, int &status,
      |                                       ~~~~~^~~~~~
injector.cpp:187:57: warning: unused parameter 'status' [-Wunused-parameter]
  187 | bool ProcessInjector::wait_for_signal(int &signal, int &status,
      |                                                    ~~~~~^~~~~~
injector.cpp:188:48: warning: unused parameter 'timeout_ms' [-Wunused-parameter]
  188 |                                       uint32_t timeout_ms) {
      |                                       ~~~~~~~~~^~~~~~~~~~
injector.cpp: In member function 'bool fuzzer::ProcessInjector::set_registers(const user_regs_struct&)':
injector.cpp:236:68: warning: unused parameter 'regs' [-Wunused-parameter]
  236 | bool ProcessInjector::set_registers(const struct user_regs_struct &regs) {
      |                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~
injector.cpp: In member function 'bool fuzzer::ProcessInjector::call_function(uintptr_t, const std::vector<unsigned char>&, uint32_t&, uint32_t)':
injector.cpp:249:47: warning: unused parameter 'function_address' [-Wunused-parameter]
  249 | bool ProcessInjector::call_function(uintptr_t function_address,
      |                                     ~~~~~~~~~~^~~~~~~~~~~~~~~~
injector.cpp:250:65: warning: unused parameter 'args' [-Wunused-parameter]
  250 |                                     const std::vector<uint8_t> &args,
      |                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~
injector.cpp:251:47: warning: unused parameter 'return_value' [-Wunused-parameter]
  251 |                                     uint32_t &return_value,
      |                                     ~~~~~~~~~~^~~~~~~~~~~~
injector.cpp:252:46: warning: unused parameter 'timeout_ms' [-Wunused-parameter]
  252 |                                     uint32_t timeout_ms) {
      |                                     ~~~~~~~~~^~~~~~~~~~
injector.cpp: In member function 'bool fuzzer::ProcessInjector::execute_and_wait(uint32_t, int&)':
injector.cpp:347:49: warning: unused parameter 'timeout_ms' [-Wunused-parameter]
  347 | bool ProcessInjector::execute_and_wait(uint32_t timeout_ms, int &exit_signal) {
      |                                        ~~~~~~~~~^~~~~~~~~~
injector.cpp:347:66: warning: unused parameter 'exit_signal' [-Wunused-parameter]
  347 | bool ProcessInjector::execute_and_wait(uint32_t timeout_ms, int &exit_signal) {
      |                                                             ~~~~~^~~~~~~~~~~
injector.cpp: In member function 'uintptr_t fuzzer::ProcessInjector::allocate_memory_in_target(size_t)':
injector.cpp:389:61: warning: unused parameter 'size' [-Wunused-parameter]
  389 | uintptr_t ProcessInjector::allocate_memory_in_target(size_t size) {
      |                                                      ~~~~~~~^~~~
injector.cpp: In function 'std::string fuzzer::get_process_name(process_id_t)':
injector.cpp:455:43: warning: unused parameter 'pid' [-Wunused-parameter]
  455 | std::string get_process_name(process_id_t pid) {
      |                              ~~~~~~~~~~~~~^~~
injector.cpp: In function 'std::string fuzzer::get_process_path(process_id_t)':
injector.cpp:469:43: warning: unused parameter 'pid' [-Wunused-parameter]
  469 | std::string get_process_path(process_id_t pid) {
      |                              ~~~~~~~~~~~~~^~~
injector.cpp: In function 'std::vector<int> fuzzer::find_processes_by_name(const std::string&)':
injector.cpp:484:69: warning: unused parameter 'name' [-Wunused-parameter]
  484 | std::vector<process_id_t> find_processes_by_name(const std::string &name) {
      |                                                  ~~~~~~~~~~~~~~~~~~~^~~~
injector.cpp: In function 'bool fuzzer::is_process_running(process_id_t)':
injector.cpp:501:38: warning: unused parameter 'pid' [-Wunused-parameter]
  501 | bool is_process_running(process_id_t pid) {
      |                         ~~~~~~~~~~~~~^~~
corpus.cpp: In member function 'bool fuzzer::Corpus::add_seed(const std::vector<unsigned char>&, const std::string&)':
corpus.cpp:87:42: warning: unused parameter 'source' [-Wunused-parameter]
   87 |                       const std::string &source) {
      |                       ~~~~~~~~~~~~~~~~~~~^~~~~~
D:/Tools/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/bin/ld.exe: fuzzer.o: in function `std::default_delete<fuzzer::Coverage>::operator()(fuzzer::Coverage*) const':
D:/Tools/mingw64/include/c++/15.1.0/bits/unique_ptr.h:93:(.text+0x50b): undefined reference to `fuzzer::Coverage::~Coverage()'
D:/Tools/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/bin/ld.exe: fuzzer.o: in function `std::__detail::_MakeUniq<fuzzer::TargetResolver>::__single_object std::make_unique<fuzzer::TargetResolver, fuzzer::ProcessInjector*>(fuzzer::ProcessInjector*&&)':
D:/Tools/mingw64/include/c++/15.1.0/bits/unique_ptr.h:1085:(.text+0x964): undefined reference to `fuzzer::TargetResolver::TargetResolver(fuzzer::ProcessInjector*)'
D:/Tools/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/bin/ld.exe: fuzzer.o: in function `fuzzer::Fuzzer::attach_to_process(unsigned int)':
D:/repos/fuzzing/fuzzer.cpp:118:(.text+0x98b): undefined reference to `fuzzer::TargetResolver::analyze_process_modules()'
D:/Tools/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/bin/ld.exe: fuzzer.o: in function `std::__detail::_MakeUniq<fuzzer::Coverage>::__single_object std::make_unique<fuzzer::Coverage, fuzzer::ProcessInjector*>(fuzzer::ProcessInjector*&&)':
D:/Tools/mingw64/include/c++/15.1.0/bits/unique_ptr.h:1085:(.text+0xac2): undefined reference to `fuzzer::Coverage::Coverage(fuzzer::ProcessInjector*)'
D:/Tools/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/bin/ld.exe: fuzzer.o: in function `std::default_delete<fuzzer::Coverage>::operator()(fuzzer::Coverage*) const':
D:/Tools/mingw64/include/c++/15.1.0/bits/unique_ptr.h:93:(.text+0xae3): undefined reference to `fuzzer::Coverage::~Coverage()'
D:/Tools/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/bin/ld.exe: fuzzer.o: in function `fuzzer::Fuzzer::set_target_function(unsigned long long)':
D:/repos/fuzzing/fuzzer.cpp:155:(.text+0xc54): undefined reference to `fuzzer::Coverage::analyze_function(unsigned long long)'
D:/Tools/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/bin/ld.exe: fuzzer.o: in function `fuzzer::Fuzzer::set_target_function(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)':
D:/repos/fuzzing/fuzzer.cpp:139:(.text+0xce2): undefined reference to `fuzzer::TargetResolver::resolve_function(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, unsigned long long&)'
D:/Tools/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/bin/ld.exe: fuzzer.o: in function `fuzzer::Fuzzer::execute_input(std::vector<unsigned char, std::allocator<unsigned char> > const&)':
D:/repos/fuzzing/fuzzer.cpp:296:(.text+0x1d42): undefined reference to `fuzzer::Coverage::has_new_coverage()'
D:/Tools/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/bin/ld.exe: fuzzer.o: in function `std::default_delete<fuzzer::Coverage>::operator()(fuzzer::Coverage*) const':
D:/Tools/mingw64/include/c++/15.1.0/bits/unique_ptr.h:93:(.text+0x2fef): undefined reference to `fuzzer::Coverage::~Coverage()'
D:/Tools/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/bin/ld.exe: fuzzer.o: in function `fuzzer::Fuzzer::run()':
D:/repos/fuzzing/fuzzer.cpp:198:(.text+0x45ed): undefined reference to `fuzzer::Coverage::start_tracking()'
D:/Tools/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/bin/ld.exe: D:/repos/fuzzing/fuzzer.cpp:256:(.text+0x479d): undefined reference to `fuzzer::Coverage::stop_tracking()'
D:/Tools/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/bin/ld.exe: fuzzer.o: in function `std::default_delete<fuzzer::Coverage>::operator()(fuzzer::Coverage*) const':
D:/Tools/mingw64/include/c++/15.1.0/bits/unique_ptr.h:93:(.text.unlikely+0x2ee): undefined reference to `fuzzer::Coverage::~Coverage()'
collect2.exe: error: ld returned 1 exit status
