fuzzer.cpp: In member function 'fuzzer::ExecutionInfo fuzzer::Fuzzer::execute_input(const std::vector<unsigned char>&)':
fuzzer.cpp:245:65: warning: unused parameter 'input' [-Wunused-parameter]
  245 | ExecutionInfo Fuzzer::execute_input(const std::vector<uint8_t>& input) {
      |                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~
injector.cpp: In member function 'bool fuzzer::ProcessInjector::attach(pid_t)':
injector.cpp:32:36: warning: unused parameter 'pid' [-Wunused-parameter]
   32 | bool ProcessInjector::attach(pid_t pid) {
      |                              ~~~~~~^~~
injector.cpp: In member function 'bool fuzzer::ProcessInjector::read_memory(uintptr_t, void*, size_t)':
injector.cpp:70:45: warning: unused parameter 'address' [-Wunused-parameter]
   70 | bool ProcessInjector::read_memory(uintptr_t address, void* buffer, size_t size) {
      |                                   ~~~~~~~~~~^~~~~~~
injector.cpp:70:60: warning: unused parameter 'buffer' [-Wunused-parameter]
   70 | bool ProcessInjector::read_memory(uintptr_t address, void* buffer, size_t size) {
      |                                                      ~~~~~~^~~~~~
injector.cpp:70:75: warning: unused parameter 'size' [-Wunused-parameter]
   70 | bool ProcessInjector::read_memory(uintptr_t address, void* buffer, size_t size) {
      |                                                                    ~~~~~~~^~~~
injector.cpp: In member function 'bool fuzzer::ProcessInjector::write_memory(uintptr_t, const void*, size_t)':
injector.cpp:94:46: warning: unused parameter 'address' [-Wunused-parameter]
   94 | bool ProcessInjector::write_memory(uintptr_t address, const void* data, size_t size) {
      |                                    ~~~~~~~~~~^~~~~~~
injector.cpp:94:67: warning: unused parameter 'data' [-Wunused-parameter]
   94 | bool ProcessInjector::write_memory(uintptr_t address, const void* data, size_t size) {
      |                                                       ~~~~~~~~~~~~^~~~
injector.cpp:94:80: warning: unused parameter 'size' [-Wunused-parameter]
   94 | bool ProcessInjector::write_memory(uintptr_t address, const void* data, size_t size) {
      |                                                                         ~~~~~~~^~~~
injector.cpp: In member function 'bool fuzzer::ProcessInjector::set_breakpoint(uintptr_t)':
injector.cpp:129:48: warning: unused parameter 'address' [-Wunused-parameter]
  129 | bool ProcessInjector::set_breakpoint(uintptr_t address) {
      |                                      ~~~~~~~~~~^~~~~~~
injector.cpp: In member function 'bool fuzzer::ProcessInjector::remove_breakpoint(uintptr_t)':
injector.cpp:151:51: warning: unused parameter 'address' [-Wunused-parameter]
  151 | bool ProcessInjector::remove_breakpoint(uintptr_t address) {
      |                                         ~~~~~~~~~~^~~~~~~
injector.cpp: In member function 'bool fuzzer::ProcessInjector::wait_for_signal(int&, int&)':
injector.cpp:185:44: warning: unused parameter 'signal' [-Wunused-parameter]
  185 | bool ProcessInjector::wait_for_signal(int& signal, int& status) {
      |                                       ~~~~~^~~~~~
injector.cpp:185:57: warning: unused parameter 'status' [-Wunused-parameter]
  185 | bool ProcessInjector::wait_for_signal(int& signal, int& status) {
      |                                                    ~~~~~^~~~~~
injector.cpp: In function 'std::string fuzzer::get_process_name(pid_t)':
injector.cpp:234:36: warning: unused parameter 'pid' [-Wunused-parameter]
  234 | std::string get_process_name(pid_t pid) {
      |                              ~~~~~~^~~
injector.cpp: In function 'std::string fuzzer::get_process_path(pid_t)':
injector.cpp:248:36: warning: unused parameter 'pid' [-Wunused-parameter]
  248 | std::string get_process_path(pid_t pid) {
      |                              ~~~~~~^~~
injector.cpp: In function 'std::vector<long long int> fuzzer::find_processes_by_name(const std::string&)':
injector.cpp:263:62: warning: unused parameter 'name' [-Wunused-parameter]
  263 | std::vector<pid_t> find_processes_by_name(const std::string& name) {
      |                                           ~~~~~~~~~~~~~~~~~~~^~~~
injector.cpp: In function 'bool fuzzer::is_process_running(pid_t)':
injector.cpp:280:31: warning: unused parameter 'pid' [-Wunused-parameter]
  280 | bool is_process_running(pid_t pid) {
      |                         ~~~~~~^~~
corpus.cpp: In member function 'bool fuzzer::Corpus::add_seed(const std::vector<unsigned char>&, const std::string&)':
corpus.cpp:86:76: warning: unused parameter 'source' [-Wunused-parameter]
   86 | bool Corpus::add_seed(const std::vector<uint8_t>& data, const std::string& source) {
      |                                                         ~~~~~~~~~~~~~~~~~~~^~~~~~
C:/Users/<USER>/scoop/apps/mingw/14.2.0-rt_v12-rev1/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/bin/ld.exe: fuzzer.o: in function `std::__detail::_MakeUniq<fuzzer::TargetResolver>::__single_object std::make_unique<fuzzer::TargetResolver, fuzzer::ProcessInjector*>(fuzzer::ProcessInjector*&&)':
C:/Users/<USER>/scoop/apps/mingw/14.2.0-rt_v12-rev1/lib/gcc/x86_64-w64-mingw32/14.2.0/include/c++/bits/unique_ptr.h:1076:(.text+0x53f): undefined reference to `fuzzer::TargetResolver::TargetResolver(fuzzer::ProcessInjector*)'
C:/Users/<USER>/scoop/apps/mingw/14.2.0-rt_v12-rev1/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/bin/ld.exe: fuzzer.o: in function `fuzzer::Fuzzer::attach_to_process(unsigned int)':
D:/Repos/fuzzing/fuzzer.cpp:97:(.text+0x566): undefined reference to `fuzzer::TargetResolver::analyze_process_modules()'
C:/Users/<USER>/scoop/apps/mingw/14.2.0-rt_v12-rev1/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/bin/ld.exe: fuzzer.o: in function `std::__detail::_MakeUniq<fuzzer::Coverage>::__single_object std::make_unique<fuzzer::Coverage, fuzzer::ProcessInjector*>(fuzzer::ProcessInjector*&&)':
C:/Users/<USER>/scoop/apps/mingw/14.2.0-rt_v12-rev1/lib/gcc/x86_64-w64-mingw32/14.2.0/include/c++/bits/unique_ptr.h:1076:(.text+0x604): undefined reference to `fuzzer::Coverage::Coverage(fuzzer::ProcessInjector*)'
C:/Users/<USER>/scoop/apps/mingw/14.2.0-rt_v12-rev1/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/bin/ld.exe: fuzzer.o: in function `std::default_delete<fuzzer::Coverage>::operator()(fuzzer::Coverage*) const':
C:/Users/<USER>/scoop/apps/mingw/14.2.0-rt_v12-rev1/lib/gcc/x86_64-w64-mingw32/14.2.0/include/c++/bits/unique_ptr.h:93:(.text+0x61f): undefined reference to `fuzzer::Coverage::~Coverage()'
C:/Users/<USER>/scoop/apps/mingw/14.2.0-rt_v12-rev1/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/bin/ld.exe: fuzzer.o: in function `fuzzer::Fuzzer::set_target_function(unsigned long long)':
D:/Repos/fuzzing/fuzzer.cpp:133:(.text+0x8d4): undefined reference to `fuzzer::Coverage::analyze_function(unsigned long long)'
C:/Users/<USER>/scoop/apps/mingw/14.2.0-rt_v12-rev1/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/bin/ld.exe: fuzzer.o: in function `fuzzer::Fuzzer::set_target_function(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)':
D:/Repos/fuzzing/fuzzer.cpp:118:(.text+0x9c2): undefined reference to `fuzzer::TargetResolver::resolve_function(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, unsigned long long&)'
C:/Users/<USER>/scoop/apps/mingw/14.2.0-rt_v12-rev1/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/bin/ld.exe: fuzzer.o: in function `fuzzer::Fuzzer::run()':
D:/Repos/fuzzing/fuzzer.cpp:176:(.text+0x1f15): undefined reference to `fuzzer::Coverage::start_tracking()'
C:/Users/<USER>/scoop/apps/mingw/14.2.0-rt_v12-rev1/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/bin/ld.exe: D:/Repos/fuzzing/fuzzer.cpp:233:(.text+0x2020): undefined reference to `fuzzer::Coverage::stop_tracking()'
C:/Users/<USER>/scoop/apps/mingw/14.2.0-rt_v12-rev1/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/bin/ld.exe: fuzzer.o: in function `std::default_delete<fuzzer::Coverage>::operator()(fuzzer::Coverage*) const':
C:/Users/<USER>/scoop/apps/mingw/14.2.0-rt_v12-rev1/lib/gcc/x86_64-w64-mingw32/14.2.0/include/c++/bits/unique_ptr.h:93:(.text+0x2b1b): undefined reference to `fuzzer::Coverage::~Coverage()'
C:/Users/<USER>/scoop/apps/mingw/14.2.0-rt_v12-rev1/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/bin/ld.exe: C:/Users/<USER>/scoop/apps/mingw/14.2.0-rt_v12-rev1/lib/gcc/x86_64-w64-mingw32/14.2.0/include/c++/bits/unique_ptr.h:93:(.text+0x2df1): undefined reference to `fuzzer::Coverage::~Coverage()'
C:/Users/<USER>/scoop/apps/mingw/14.2.0-rt_v12-rev1/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/bin/ld.exe: C:/Users/<USER>/scoop/apps/mingw/14.2.0-rt_v12-rev1/lib/gcc/x86_64-w64-mingw32/14.2.0/include/c++/bits/unique_ptr.h:93:(.text+0x308f): undefined reference to `fuzzer::Coverage::~Coverage()'
collect2.exe: error: ld returned 1 exit status
