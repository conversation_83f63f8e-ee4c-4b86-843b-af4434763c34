#pragma once

#include <atomic>
#include <chrono>
#include <cstdint>
#include <cstring>
#include <memory>
#include <string>
#include <vector>

#ifdef _WIN32
#include <process.h>
#include <windows.h>
#else
#include <fcntl.h>
#include <semaphore.h>
#include <setjmp.h>
#include <signal.h>
#include <sys/mman.h>
#include <sys/stat.h>
#include <unistd.h>
#endif

namespace fuzzer {

// Communication protocol constants
constexpr size_t MAX_INPUT_SIZE = 64 * 1024; // 64KB max input
constexpr size_t MAX_FUNCTION_NAME = 256;
constexpr uint32_t MAGIC_HEADER = 0xFEEDFACE;
constexpr uint32_t PROTOCOL_VERSION = 1;

// Command types for fuzzer <-> agent communication
enum class AgentCommand : uint32_t {
  PING = 1,
  SET_TARGET_FUNCTION = 2,
  EXECUTE_INPUT = 3,
  GET_COVERAGE = 4,
  RESET_COVERAGE = 5,
  GET_STATS = 6,
  SHUTDOWN = 7
};

// Response types
enum class AgentResponse : uint32_t {
  SUCCESS = 1,
  ERROR_ = 2,
  CRASH = 3,
  TIMEOUT = 4,
  FUNCTION_NOT_FOUND = 5
};

// Execution result structure
struct AgentExecutionResult {
  AgentResponse result;
  uint32_t return_value;
  uint32_t execution_time_us;
  uint32_t signal_number;
  uint32_t coverage_count;
  char crash_info[256];
};

// Coverage information
struct CoverageInfo {
  uint32_t basic_block_count;
  uint32_t edge_count;
  uint32_t function_count;
  uint64_t coverage_bitmap[1024]; // Simple bitmap for basic blocks
};

// Shared memory structure for communication
struct SharedMemoryLayout {
  // Header
  uint32_t magic;
  uint32_t version;
  std::atomic<uint32_t> sequence_number;

  // Command/Response
  std::atomic<AgentCommand> command;
  std::atomic<AgentResponse> response;
  std::atomic<bool> command_ready;
  std::atomic<bool> response_ready;

  // Function target information
  char target_function_name[MAX_FUNCTION_NAME];
  uintptr_t target_function_address;

  // Input data
  uint32_t input_size;
  uint8_t input_data[MAX_INPUT_SIZE];

  // Execution result
  AgentExecutionResult exec_result;

  // Coverage data
  CoverageInfo coverage;

  // Statistics
  uint64_t total_executions;
  uint64_t total_crashes;
  uint64_t total_timeouts;

  // Padding to page boundary
  uint8_t
      padding[4096 - ((sizeof(uint32_t) * 6 + sizeof(std::atomic<uint32_t>) +
                       sizeof(std::atomic<AgentCommand>) +
                       sizeof(std::atomic<AgentResponse>) +
                       sizeof(std::atomic<bool>) * 2 + MAX_FUNCTION_NAME +
                       sizeof(uintptr_t) + sizeof(uint32_t) + MAX_INPUT_SIZE +
                       sizeof(AgentExecutionResult) + sizeof(CoverageInfo) +
                       sizeof(uint64_t) * 3) %
                      4096)];
};

// Function pointer type for target functions
using TargetFunction = int (*)(const uint8_t *data, size_t size);

// Agent class - runs inside the target process
class FuzzAgent {
public:
  FuzzAgent();
  ~FuzzAgent();

  // Initialization
  bool initialize(const std::string &shm_name);
  void shutdown();

  // Main agent loop
  void run();

  // Target function management
  bool set_target_function(const std::string &function_name);
  bool set_target_function(uintptr_t function_address);

  // Execution
  AgentExecutionResult execute_input(const uint8_t *data, size_t size);

  // Coverage tracking
  void reset_coverage();
  CoverageInfo get_coverage() const;

  // Signal handling
  static void signal_handler(int sig);
  static void setup_signal_handlers();

private:
  SharedMemoryLayout *shared_memory_;
  std::string shm_name_;
  int shm_fd_;
  bool initialized_;
  bool should_stop_;

  // Target function
  TargetFunction target_function_;
  std::string target_function_name_;
  uintptr_t target_function_address_;

  // Coverage tracking
  std::vector<uintptr_t> coverage_points_;
  std::atomic<uint32_t> coverage_count_;

  // Signal handling for crash detection
  static thread_local sigjmp_buf crash_jmp_buf_;
  static thread_local bool in_target_execution_;
  static thread_local FuzzAgent *current_agent_;

  // Statistics
  std::atomic<uint64_t> total_executions_;
  std::atomic<uint64_t> total_crashes_;
  std::atomic<uint64_t> total_timeouts_;

  // Internal methods
  bool create_shared_memory();
  void cleanup_shared_memory();
  bool resolve_function_address(const std::string &function_name,
                                uintptr_t &address);
  void handle_command();
  void send_response(AgentResponse response);
  void update_statistics();

  // Coverage instrumentation
  void instrument_function(uintptr_t function_address);
  void add_coverage_point(uintptr_t address);

  // Crash handling
  void handle_crash(int signal);
  bool execute_with_crash_detection(const uint8_t *data, size_t size,
                                    AgentExecutionResult &result);
};

// C interface for library loading
extern "C" {
// Entry point called when library is loaded
void fuzz_agent_init();

// Entry point called when library is unloaded
void fuzz_agent_cleanup();

// Manual control functions
int fuzz_agent_start(const char *shm_name);
void fuzz_agent_stop();

// Direct execution interface (for testing)
int fuzz_agent_execute(const uint8_t *data, size_t size);
}

// Utility functions
namespace agent_utils {
std::string get_process_name();
pid_t get_process_id();
std::vector<std::string> get_loaded_modules();
bool is_function_address_valid(uintptr_t address);
std::string get_function_name_at_address(uintptr_t address);

// Symbol resolution helpers
uintptr_t resolve_symbol(const std::string &symbol_name);
uintptr_t resolve_symbol_in_module(const std::string &symbol_name,
                                   const std::string &module_name);

// Memory protection helpers
bool make_memory_executable(void *address, size_t size);
bool protect_memory(void *address, size_t size, int protection);
} // namespace agent_utils

} // namespace fuzzer
