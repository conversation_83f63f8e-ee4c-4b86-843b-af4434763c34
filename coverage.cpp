#include "coverage.hpp"
#include "injector.hpp"

#include <iostream>
#include <iomanip>

namespace fuzzer {

Coverage::Coverage(ProcessInjector* injector)
    : injector_(injector), coverage_bitmap_(BITMAP_SIZE, 0), previous_bitmap_(BITMAP_SIZE, 0) {
}

Coverage::~Coverage() {
    if (tracking_enabled_) {
        stop_tracking();
    }
}

bool Coverage::initialize(uintptr_t start_address, uintptr_t end_address) {
    std::cout << "[+] Initializing coverage tracking from 0x" << std::hex
              << start_address << " to 0x" << end_address << std::dec << '\n';

    // Find basic blocks in the specified range
    return find_basic_blocks(start_address, end_address);
}

bool Coverage::analyze_function(uintptr_t function_address) {
    std::cout << "[+] Analyzing function at 0x" << std::hex
              << function_address << std::dec << " for coverage" << '\n';

    // Simplified implementation - would use disassembly to find function bounds
    uintptr_t start = function_address;
    uintptr_t end = function_address + 1024; // Assume max 1KB function

    return initialize(start, end);
}

bool Coverage::analyze_module(const std::string& module_name) {
    std::cout << "[+] Analyzing module " << module_name << " for coverage" << '\n';

    // Simplified implementation
    return true;
}

bool Coverage::start_tracking() {
    if (tracking_enabled_) {
        return true;
    }

    std::cout << "[+] Starting coverage tracking" << '\n';

    // Set breakpoints on basic blocks
    if (!set_coverage_breakpoints()) {
        std::cerr << "[-] Failed to set coverage breakpoints" << '\n';
        return false;
    }

    tracking_enabled_ = true;
    return true;
}

bool Coverage::stop_tracking() {
    if (!tracking_enabled_) {
        return true;
    }

    std::cout << "[+] Stopping coverage tracking" << '\n';

    // Remove breakpoints
    remove_coverage_breakpoints();

    tracking_enabled_ = false;
    return true;
}

bool Coverage::reset_coverage() {
    std::fill(coverage_bitmap_.begin(), coverage_bitmap_.end(), 0);

    // Reset hit counts
    for (auto& block : basic_blocks_) {
        block.is_hit = false;
        block.hit_count = 0;
    }

    for (auto& edge : edges_) {
        const_cast<Edge&>(edge).is_hit = false;
        const_cast<Edge&>(edge).hit_count = 0;
    }

    return true;
}

bool Coverage::has_new_coverage() {
    // Compare current bitmap with previous
    bool new_coverage = false;
    for (size_t i = 0; i < coverage_bitmap_.size(); ++i) {
        if (coverage_bitmap_[i] != previous_bitmap_[i]) {
            new_coverage = true;
            break;
        }
    }

    // Update previous bitmap
    previous_bitmap_ = coverage_bitmap_;

    return new_coverage;
}

size_t Coverage::get_covered_blocks() const {
    size_t covered = 0;
    for (const auto& block : basic_blocks_) {
        if (block.is_hit) {
            covered++;
        }
    }
    return covered;
}

size_t Coverage::get_covered_edges() const {
    size_t covered = 0;
    for (const auto& edge : edges_) {
        if (edge.is_hit) {
            covered++;
        }
    }
    return covered;
}

double Coverage::get_coverage_percentage() const {
    if (basic_blocks_.empty()) {
        return 0.0;
    }

    return (static_cast<double>(get_covered_blocks()) / basic_blocks_.size()) * 100.0;
}

bool Coverage::compare_coverage(const std::vector<uint8_t>& other_bitmap) {
    if (other_bitmap.size() != coverage_bitmap_.size()) {
        return false;
    }

    for (size_t i = 0; i < coverage_bitmap_.size(); ++i) {
        if (coverage_bitmap_[i] != other_bitmap[i]) {
            return false;
        }
    }

    return true;
}

bool Coverage::set_coverage_breakpoints() {
    if (!injector_) {
        return false;
    }

    // Set breakpoints on all basic block start addresses
    for (const auto& block : basic_blocks_) {
        if (!injector_->set_breakpoint(block.start_address)) {
            std::cerr << "[-] Failed to set breakpoint at 0x" << std::hex
                      << block.start_address << std::dec << '\n';
            return false;
        }
        active_breakpoints_.insert(block.start_address);
    }

    return true;
}

bool Coverage::remove_coverage_breakpoints() {
    if (!injector_) {
        return false;
    }

    // Remove all active breakpoints
    for (uintptr_t address : active_breakpoints_) {
        injector_->remove_breakpoint(address);
    }

    active_breakpoints_.clear();
    return true;
}

bool Coverage::handle_breakpoint(uintptr_t address) {
    // Find the basic block for this address
    auto it = address_to_block_.find(address);
    if (it == address_to_block_.end()) {
        return false;
    }

    size_t block_index = it->second;
    mark_block_covered(block_index);

    // Update coverage bitmap
    update_coverage_bitmap(last_block_address_, address);
    last_block_address_ = address;

    return true;
}

void Coverage::print_coverage_stats() const {
    std::cout << "\n=== Coverage Statistics ===" << '\n';
    std::cout << "Total blocks: " << basic_blocks_.size() << '\n';
    std::cout << "Covered blocks: " << get_covered_blocks() << '\n';
    std::cout << "Coverage: " << std::fixed << std::setprecision(2)
              << get_coverage_percentage() << "%" << '\n';
    std::cout << "Total edges: " << edges_.size() << '\n';
    std::cout << "Covered edges: " << get_covered_edges() << '\n';
    std::cout << "===========================" << '\n';
}

// Private methods
bool Coverage::find_basic_blocks(uintptr_t start_address, uintptr_t end_address) {
    // Simplified implementation - create dummy basic blocks
    for (uintptr_t addr = start_address; addr < end_address; addr += 16) {
        BasicBlock block;
        block.start_address = addr;
        block.end_address = addr + 15;
        block.size = 16;
        block.is_hit = false;
        block.hit_count = 0;

        address_to_block_[addr] = basic_blocks_.size();
        basic_blocks_.push_back(block);
    }

    std::cout << "[+] Found " << basic_blocks_.size() << " basic blocks" << '\n';
    return true;
}

bool Coverage::analyze_control_flow() {
    // Simplified implementation
    return true;
}

void Coverage::update_coverage_bitmap(uintptr_t from_addr, uintptr_t to_addr) {
    uint32_t hash = hash_addresses(from_addr, to_addr);
    size_t index = hash % coverage_bitmap_.size();

    if (coverage_bitmap_[index] < 255) {
        coverage_bitmap_[index]++;
    }
}

uint32_t Coverage::hash_addresses(uintptr_t addr1, uintptr_t addr2) {
    // Simple hash function
    return (addr1 >> 4) ^ (addr2 >> 4);
}

void Coverage::mark_block_covered(size_t block_index) {
    if (block_index < basic_blocks_.size()) {
        basic_blocks_[block_index].is_hit = true;
        basic_blocks_[block_index].hit_count++;
    }
}

void Coverage::mark_edge_covered(const Edge& edge) {
    auto it = edges_.find(edge);
    if (it != edges_.end()) {
        const_cast<Edge&>(*it).is_hit = true;
        const_cast<Edge&>(*it).hit_count++;
    }
}

// SimpleDisassembler implementation
SimpleDisassembler::DisasmResult SimpleDisassembler::disassemble_at(ProcessInjector* injector, uintptr_t address) {
    DisasmResult result;
    result.success = false;

    if (!injector) {
        return result;
    }

    // Read instruction bytes
    uint8_t bytes[16];
    if (!injector->read_memory(address, bytes, sizeof(bytes))) {
        return result;
    }

    // Simplified x86-32 instruction analysis
    analyze_instruction(bytes, sizeof(bytes), address, result);
    return result;
}

bool SimpleDisassembler::analyze_instruction(const uint8_t* bytes, size_t max_length,
                                           uintptr_t address, DisasmResult& result) {
    if (max_length == 0) {
        return false;
    }

    uint8_t opcode = bytes[0];

    // Simplified instruction analysis
    result.success = true;
    result.instruction_length = 1; // Simplified
    result.is_branch = is_branch_opcode(opcode);
    result.is_call = is_call_opcode(opcode);
    result.is_return = is_return_opcode(opcode);
    result.is_conditional = false;
    result.target_address = 0;
    result.mnemonic = "unknown";

    return true;
}

bool SimpleDisassembler::is_branch_opcode(uint8_t opcode) {
    // Simplified branch detection
    return (opcode >= 0x70 && opcode <= 0x7F) || // Jcc short
           (opcode == 0xEB) ||                    // JMP short
           (opcode == 0xE9);                      // JMP near
}

bool SimpleDisassembler::is_call_opcode(uint8_t opcode) {
    return opcode == 0xE8; // CALL near
}

bool SimpleDisassembler::is_return_opcode(uint8_t opcode) {
    return opcode == 0xC3 || opcode == 0xC2; // RET
}

} // namespace fuzzer
