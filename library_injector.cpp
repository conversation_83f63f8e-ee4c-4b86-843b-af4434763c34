#include "library_injector.hpp"
#include "injector.hpp"
#include <chrono>
#include <fstream>
#include <iostream>
#include <thread>

#ifdef _WIN32
#include <psapi.h>
#include <tlhelp32.h>
#include <windows.h>
#else
#include <dlfcn.h>
#include <fcntl.h>
#include <signal.h>
#include <sys/mman.h>
#include <sys/stat.h>
#include <unistd.h>
#endif

namespace fuzzer {

LibraryInjector::LibraryInjector()
    : attached_pid_(0), agent_injected_(false), shared_memory_(nullptr),
      shm_fd_(-1) {}

LibraryInjector::~LibraryInjector() {
  if (agent_injected_) {
    shutdown_agent();
  }

  if (is_attached()) {
    detach_from_process();
  }

  cleanup_shared_memory();
}

bool LibraryInjector::attach_to_process(uint32_t pid) {
  if (is_attached()) {
    std::cerr << "[INJECTOR] Already attached to process " << attached_pid_
              << std::endl;
    return false;
  }

  if (!injection_utils::is_process_running(pid)) {
    std::cerr << "[INJECTOR] Process " << pid << " is not running" << std::endl;
    return false;
  }

  if (!injection_utils::can_inject_into_process(pid)) {
    std::cerr << "[INJECTOR] Cannot inject into process " << pid << std::endl;
    return false;
  }

  // Create process injector for library injection
  process_injector_ = std::make_unique<ProcessInjector>();
  if (!process_injector_->attach(pid)) {
    std::cerr << "[INJECTOR] Failed to attach to process " << pid << std::endl;
    return false;
  }

  attached_pid_ = pid;
  shm_name_ = "/fuzz_agent_" + std::to_string(pid);

  std::cout << "[INJECTOR] Successfully attached to process " << pid
            << std::endl;
  return true;
}

bool LibraryInjector::detach_from_process() {
  if (!is_attached()) {
    return true;
  }

  if (process_injector_) {
    process_injector_->detach();
    process_injector_.reset();
  }

  attached_pid_ = 0;
  agent_injected_ = false;

  std::cout << "[INJECTOR] Detached from process" << std::endl;
  return true;
}

bool LibraryInjector::inject_agent_library(const std::string &library_path) {
  if (!is_attached()) {
    std::cerr << "[INJECTOR] Not attached to any process" << std::endl;
    return false;
  }

  if (agent_injected_) {
    std::cout << "[INJECTOR] Agent already injected" << std::endl;
    return true;
  }

  std::string lib_path =
      library_path.empty() ? get_default_library_path() : library_path;

  if (!library_exists(lib_path)) {
    std::cerr << "[INJECTOR] Library not found: " << lib_path << std::endl;
    return false;
  }

  std::cout << "[INJECTOR] Injecting library: " << lib_path << std::endl;

  // Create shared memory before injection
  if (!create_shared_memory()) {
    std::cerr << "[INJECTOR] Failed to create shared memory" << std::endl;
    return false;
  }

  // Inject library using process injector
  bool success = false;
#ifdef _WIN32
  success = inject_library_windows(attached_pid_, lib_path);
#else
  success = inject_library_linux(attached_pid_, lib_path);
#endif

  if (!success) {
    std::cerr << "[INJECTOR] Failed to inject library" << std::endl;
    cleanup_shared_memory();
    return false;
  }

  // Wait for agent to initialize
  std::this_thread::sleep_for(std::chrono::milliseconds(500));

  // Test communication with agent
  if (!ping_agent(5000)) {
    std::cerr << "[INJECTOR] Failed to communicate with injected agent"
              << std::endl;
    cleanup_shared_memory();
    return false;
  }

  agent_injected_ = true;
  std::cout << "[INJECTOR] Agent library injected and initialized successfully"
            << std::endl;

  return true;
}

bool LibraryInjector::ping_agent(uint32_t timeout_ms) {
  if (!agent_injected_ || !shared_memory_) {
    return false;
  }

  return send_command(AgentCommand::PING, timeout_ms);
}

bool LibraryInjector::set_target_function(const std::string &function_name) {
  if (!agent_injected_ || !shared_memory_) {
    std::cerr << "[INJECTOR] Agent not injected" << std::endl;
    return false;
  }

  // Clear previous data
  memset(shared_memory_->target_function_name, 0,
         sizeof(shared_memory_->target_function_name));
  shared_memory_->target_function_address = 0;

  // Set function name
  strncpy(shared_memory_->target_function_name, function_name.c_str(),
          sizeof(shared_memory_->target_function_name) - 1);

  bool success = send_command(AgentCommand::SET_TARGET_FUNCTION);
  if (success) {
    std::cout << "[INJECTOR] Target function set to: " << function_name
              << std::endl;
  } else {
    std::cerr << "[INJECTOR] Failed to set target function: " << function_name
              << std::endl;
  }

  return success;
}

bool LibraryInjector::set_target_function(uintptr_t function_address) {
  if (!agent_injected_ || !shared_memory_) {
    std::cerr << "[INJECTOR] Agent not injected" << std::endl;
    return false;
  }

  // Clear previous data
  memset(shared_memory_->target_function_name, 0,
         sizeof(shared_memory_->target_function_name));
  shared_memory_->target_function_address = function_address;

  bool success = send_command(AgentCommand::SET_TARGET_FUNCTION);
  if (success) {
    std::cout << "[INJECTOR] Target function set to address: 0x" << std::hex
              << function_address << std::endl;
  } else {
    std::cerr << "[INJECTOR] Failed to set target function address: 0x"
              << std::hex << function_address << std::endl;
  }

  return success;
}

AgentExecutionResult
LibraryInjector::execute_input(const std::vector<uint8_t> &input,
                               uint32_t timeout_ms) {
  AgentExecutionResult result = {};
  result.result = AgentResponse::ERROR_;

  if (!agent_injected_ || !shared_memory_) {
    std::cerr << "[INJECTOR] Agent not injected" << std::endl;
    return result;
  }

  if (input.size() > MAX_INPUT_SIZE) {
    std::cerr << "[INJECTOR] Input too large: " << input.size() << " bytes"
              << std::endl;
    return result;
  }

  // Copy input data
  shared_memory_->input_size = static_cast<uint32_t>(input.size());
  memcpy(shared_memory_->input_data, input.data(), input.size());

  // Send execute command
  if (send_command(AgentCommand::EXECUTE_INPUT, timeout_ms)) {
    result = shared_memory_->exec_result;
  }

  return result;
}

CoverageInfo LibraryInjector::get_coverage() {
  CoverageInfo info = {};

  if (agent_injected_ && shared_memory_ &&
      send_command(AgentCommand::GET_COVERAGE)) {
    info = shared_memory_->coverage;
  }

  return info;
}

void LibraryInjector::reset_coverage() {
  if (agent_injected_ && shared_memory_) {
    send_command(AgentCommand::RESET_COVERAGE);
  }
}

LibraryInjector::AgentStats LibraryInjector::get_agent_stats() {
  AgentStats stats = {};

  if (agent_injected_ && shared_memory_ &&
      send_command(AgentCommand::GET_STATS)) {
    stats.total_executions = shared_memory_->total_executions;
    stats.total_crashes = shared_memory_->total_crashes;
    stats.total_timeouts = shared_memory_->total_timeouts;
    stats.coverage_count = shared_memory_->coverage.basic_block_count;
  }

  return stats;
}

bool LibraryInjector::shutdown_agent() {
  if (!agent_injected_ || !shared_memory_) {
    return true;
  }

  bool success = send_command(AgentCommand::SHUTDOWN, 2000);
  agent_injected_ = false;

  std::cout << "[INJECTOR] Agent shutdown "
            << (success ? "successful" : "failed") << std::endl;
  return success;
}

bool LibraryInjector::create_shared_memory() {
#ifdef _WIN32
  // Windows implementation would go here
  return false;
#else
  // Create shared memory object
  shm_fd_ = shm_open(shm_name_.c_str(), O_CREAT | O_RDWR, 0666);
  if (shm_fd_ == -1) {
    perror("shm_open");
    return false;
  }

  // Set size
  if (ftruncate(shm_fd_, sizeof(SharedMemoryLayout)) == -1) {
    perror("ftruncate");
    close(shm_fd_);
    return false;
  }

  // Map memory
  shared_memory_ = static_cast<SharedMemoryLayout *>(
      mmap(nullptr, sizeof(SharedMemoryLayout), PROT_READ | PROT_WRITE,
           MAP_SHARED, shm_fd_, 0));

  if (shared_memory_ == MAP_FAILED) {
    perror("mmap");
    close(shm_fd_);
    return false;
  }

  return true;
#endif
}

void LibraryInjector::cleanup_shared_memory() {
#ifndef _WIN32
  if (shared_memory_) {
    munmap(shared_memory_, sizeof(SharedMemoryLayout));
    shared_memory_ = nullptr;
  }

  if (shm_fd_ != -1) {
    close(shm_fd_);
    shm_unlink(shm_name_.c_str());
    shm_fd_ = -1;
  }
#endif
}

bool LibraryInjector::wait_for_response(uint32_t timeout_ms) {
  auto start_time = std::chrono::steady_clock::now();

  while (true) {
    if (shared_memory_->response_ready.load()) {
      shared_memory_->response_ready.store(false);
      return true;
    }

    auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(
                       std::chrono::steady_clock::now() - start_time)
                       .count();

    if (elapsed >= timeout_ms) {
      return false;
    }

    std::this_thread::sleep_for(std::chrono::milliseconds(10));
  }
}

bool LibraryInjector::send_command(AgentCommand command, uint32_t timeout_ms) {
  if (!shared_memory_) {
    return false;
  }

  // Set command
  shared_memory_->command.store(command);
  shared_memory_->command_ready.store(true);

  // Wait for response
  if (!wait_for_response(timeout_ms)) {
    std::cerr << "[INJECTOR] Command timeout" << std::endl;
    return false;
  }

  // Check response
  AgentResponse response = shared_memory_->response.load();
  return response == AgentResponse::SUCCESS;
}

bool LibraryInjector::inject_library_windows(uint32_t pid,
                                             const std::string &library_path) {
#ifdef _WIN32
  // Use ProcessInjector's inject_library method
  return process_injector_->inject_library(library_path);
#else
  (void)pid;
  (void)library_path;
  return false;
#endif
}

bool LibraryInjector::inject_library_linux(uint32_t pid,
                                           const std::string &library_path) {
#ifndef _WIN32
  // Use ProcessInjector's inject_library method
  return process_injector_->inject_library(library_path);
#else
  (void)pid;
  (void)library_path;
  return false;
#endif
}

std::string LibraryInjector::get_default_library_path() {
#ifdef _WIN32
  return "./fuzz_agent.dll";
#else
  return "./libfuzz_agent.so";
#endif
}

bool LibraryInjector::library_exists(const std::string &path) {
  std::ifstream file(path);
  return file.good();
}

} // namespace fuzzer
