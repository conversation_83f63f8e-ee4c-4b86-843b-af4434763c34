# Library Injection Fuzzer

This document describes the new library injection-based fuzzing system that provides a more robust and efficient alternative to direct process manipulation.

## Overview

The library injection fuzzer consists of three main components:

1. **Fuzz Agent Library** (`libfuzz_agent.so`) - Injected into the target process
2. **Library Injector** - Manages injection and communication
3. **Library-based Fuzzer** - High-level fuzzing interface

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   lib_fuzzer    │    │ libfuzz_agent.so│    │  Target Process │
│                 │    │                 │    │                 │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │LibraryFuzzer│ │    │ │ FuzzAgent   │ │    │ │Target Func  │ │
│ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │
│        │        │    │        │        │    │        │        │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │                 │
│ │LibraryInject│◄┼────┼►│SharedMemory │ │    │                 │
│ └─────────────┘ │    │ └─────────────┘ │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Key Features

### 1. Shared Memory Communication
- Fast IPC using POSIX shared memory
- Lock-free atomic operations
- Configurable timeouts
- Protocol versioning

### 2. Crash Detection
- Signal handling within target process
- Setjmp/longjmp for crash recovery
- Detailed crash information
- Stack trace capture (future)

### 3. Coverage Tracking
- Basic block coverage bitmap
- Edge coverage tracking
- Function-level instrumentation
- Real-time coverage updates

### 4. Cross-Platform Support
- Linux implementation (primary)
- Windows stubs (future implementation)
- Architecture detection
- Permission checking

## Building

### Prerequisites
```bash
# Ubuntu/Debian
sudo apt-get install build-essential gcc-multilib g++-multilib libc6-dev-i386

# Additional libraries
sudo apt-get install librt-dev
```

### Compilation
```bash
# Build all components
make all

# Build specific components
make libfuzz_agent.so    # Shared library
make lib_fuzzer          # Library-based fuzzer
make test_target         # Test target
```

### Build Outputs
- `libfuzz_agent.so` - Agent library for injection
- `lib_fuzzer` - Main fuzzer executable
- `test_target` - Vulnerable test program
- `fuzzer` - Original direct-injection fuzzer

## Usage

### Basic Usage
```bash
# Start test target
./test_target &
TARGET_PID=$!

# Run library-based fuzzer
./lib_fuzzer -p $TARGET_PID -f fuzz_target
```

### Advanced Usage
```bash
# Custom library path
./lib_fuzzer -p $TARGET_PID -f fuzz_target -l ./custom_agent.so

# Specific function address
./lib_fuzzer -p $TARGET_PID -a 0x08048000

# Custom corpus and seeds
./lib_fuzzer -p $TARGET_PID -f vulnerable_func -c ./seeds -s seed1.bin

# High-performance mode
./lib_fuzzer -p $TARGET_PID -f target --no-coverage -i 10000000
```

### Command Line Options
```
-p, --pid PID              Target process PID
-f, --function NAME        Target function name
-a, --address ADDR         Target function address (hex)
-l, --library PATH         Agent library path
-c, --corpus DIR           Corpus directory
-o, --output DIR           Output directory
-s, --seed FILE            Seed file(s) to load
-i, --iterations NUM       Maximum iterations
-t, --timeout MS           Timeout in milliseconds
-v, --verbose              Verbose output
--no-coverage              Disable coverage tracking
--help                     Show help message
```

## Communication Protocol

### Shared Memory Layout
```c
struct SharedMemoryLayout {
    uint32_t magic;                    // Protocol magic
    uint32_t version;                  // Protocol version
    atomic<uint32_t> sequence_number;  // Message sequence
    
    // Command/Response
    atomic<AgentCommand> command;      // Command from fuzzer
    atomic<AgentResponse> response;    // Response from agent
    atomic<bool> command_ready;        // Command ready flag
    atomic<bool> response_ready;       // Response ready flag
    
    // Function target
    char target_function_name[256];    // Function name
    uintptr_t target_function_address; // Function address
    
    // Input data
    uint32_t input_size;               // Input size
    uint8_t input_data[64KB];          // Input buffer
    
    // Execution result
    ExecutionResult exec_result;       // Execution result
    
    // Coverage data
    CoverageInfo coverage;             // Coverage information
    
    // Statistics
    uint64_t total_executions;         // Total executions
    uint64_t total_crashes;            // Total crashes
    uint64_t total_timeouts;           // Total timeouts
};
```

### Commands
- `PING` - Test communication
- `SET_TARGET_FUNCTION` - Set target function
- `EXECUTE_INPUT` - Execute input data
- `GET_COVERAGE` - Get coverage information
- `RESET_COVERAGE` - Reset coverage data
- `GET_STATS` - Get execution statistics
- `SHUTDOWN` - Shutdown agent

### Responses
- `SUCCESS` - Operation successful
- `ERROR` - General error
- `CRASH` - Target function crashed
- `TIMEOUT` - Execution timeout
- `FUNCTION_NOT_FOUND` - Function not found

## Advantages over Direct Injection

### 1. Stability
- No direct process manipulation
- Isolated crash handling
- Graceful error recovery
- Clean shutdown

### 2. Performance
- Shared memory communication
- No ptrace overhead
- Minimal context switching
- Efficient data transfer

### 3. Flexibility
- Dynamic function targeting
- Runtime configuration
- Coverage customization
- Extensible protocol

### 4. Debugging
- Better error reporting
- Detailed crash information
- Agent-side logging
- Protocol debugging

## Implementation Details

### Agent Library (`fuzz_agent.cpp`)
- Runs inside target process
- Handles function execution
- Manages crash detection
- Tracks coverage information
- Communicates via shared memory

### Library Injector (`library_injector.cpp`)
- Manages process attachment
- Handles library injection
- Implements communication protocol
- Provides high-level API
- Manages shared memory

### Main Fuzzer (`lib_main.cpp`)
- Command-line interface
- Corpus management
- Fuzzing loop
- Statistics reporting
- Signal handling

## Testing

### Test Target
The `test_target` program provides several vulnerable functions:
- `fuzz_target` - Main fuzzing target
- `vulnerable_strcpy` - Buffer overflow
- `vulnerable_printf` - Format string
- `vulnerable_heap` - Heap corruption
- `vulnerable_int_overflow` - Integer overflow

### Example Session
```bash
# Terminal 1: Start test target
./test_target &
echo "Target PID: $!"

# Terminal 2: Run fuzzer
./lib_fuzzer -p $! -f fuzz_target -v

# Expected output:
# [+] Attaching to process 1234...
# [+] Injecting agent library...
# [AGENT] Library loaded into process 1234
# [AGENT] Agent started successfully
# [+] Setting target function...
# [+] Starting fuzzing campaign...
# [!] Crash found! Signal: 11, Execution: 1337
```

## Troubleshooting

### Common Issues

1. **Permission Denied**
   ```bash
   # Run with appropriate privileges
   sudo ./lib_fuzzer -p $PID -f function_name
   ```

2. **Library Not Found**
   ```bash
   # Check library path
   ls -la libfuzz_agent.so
   ./lib_fuzzer -l ./libfuzz_agent.so -p $PID -f function_name
   ```

3. **Function Not Found**
   ```bash
   # Check available symbols
   nm -D ./test_target | grep function_name
   objdump -t ./test_target | grep function_name
   ```

4. **Communication Timeout**
   ```bash
   # Increase timeout
   ./lib_fuzzer -p $PID -f function_name -t 5000
   ```

### Debug Mode
```bash
# Build with debug symbols
make debug

# Run with verbose output
./lib_fuzzer -p $PID -f function_name -v

# Check shared memory
ls -la /dev/shm/fuzz_agent_*
```

## Future Enhancements

1. **Windows Support** - Complete Windows implementation
2. **Advanced Coverage** - Control flow and data flow tracking
3. **Mutation Engine** - Sophisticated input generation
4. **Crash Analysis** - Stack trace and root cause analysis
5. **Distributed Fuzzing** - Multi-process coordination
6. **GUI Interface** - Real-time monitoring and control

## Contributing

1. Fork the repository
2. Create a feature branch
3. Implement changes
4. Add tests
5. Submit pull request

## License

Educational and security research use only.
