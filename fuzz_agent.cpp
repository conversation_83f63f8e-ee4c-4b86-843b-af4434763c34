#include "fuzz_agent.hpp"
#include <chrono>
#include <cstdlib>
#include <cstring>
#include <iostream>
#include <set>
#include <thread>

#ifdef _WIN32
#include <process.h>
#include <psapi.h>
#include <windows.h>
#define getpid _getpid
#else
#include <dlfcn.h>
#include <fcntl.h>
#include <semaphore.h>
#include <setjmp.h>
#include <signal.h>
#include <sys/mman.h>
#include <sys/stat.h>
#include <unistd.h>
#endif

namespace fuzzer {

// Static members
thread_local sigjmp_buf FuzzAgent::crash_jmp_buf_;
thread_local bool FuzzAgent::in_target_execution_ = false;
thread_local FuzzAgent *FuzzAgent::current_agent_ = nullptr;

FuzzAgent::FuzzAgent()
    : shared_memory_(nullptr), shm_fd_(-1), initialized_(false),
      should_stop_(false), target_function_(nullptr),
      target_function_address_(0), coverage_count_(0), total_executions_(0),
      total_crashes_(0), total_timeouts_(0) {

  current_agent_ = this;
}

FuzzAgent::~FuzzAgent() { shutdown(); }

bool FuzzAgent::initialize(const std::string &shm_name) {
  shm_name_ = shm_name;

  std::cout << "[AGENT] Initializing fuzz agent in process " << getpid()
            << std::endl;

  if (!create_shared_memory()) {
    std::cerr << "[AGENT] Failed to create shared memory" << std::endl;
    return false;
  }

  setup_signal_handlers();

  // Initialize shared memory structure
  shared_memory_->magic = MAGIC_HEADER;
  shared_memory_->version = PROTOCOL_VERSION;
  shared_memory_->sequence_number.store(0);
  shared_memory_->command.store(AgentCommand::PING);
  shared_memory_->response.store(AgentResponse::SUCCESS);
  shared_memory_->command_ready.store(false);
  shared_memory_->response_ready.store(false);
  shared_memory_->total_executions = 0;
  shared_memory_->total_crashes = 0;
  shared_memory_->total_timeouts = 0;

  initialized_ = true;
  std::cout << "[AGENT] Fuzz agent initialized successfully" << std::endl;

  return true;
}

void FuzzAgent::shutdown() {
  if (!initialized_)
    return;

  should_stop_ = true;
  cleanup_shared_memory();
  initialized_ = false;

  std::cout << "[AGENT] Fuzz agent shutdown complete" << std::endl;
}

void FuzzAgent::run() {
  if (!initialized_) {
    std::cerr << "[AGENT] Agent not initialized" << std::endl;
    return;
  }

  std::cout << "[AGENT] Starting agent main loop" << std::endl;

  while (!should_stop_) {
    // Check for commands from fuzzer
    if (shared_memory_->command_ready.load()) {
      handle_command();
      shared_memory_->command_ready.store(false);
    }

    // Small sleep to avoid busy waiting
    std::this_thread::sleep_for(std::chrono::microseconds(100));
  }

  std::cout << "[AGENT] Agent main loop terminated" << std::endl;
}

bool FuzzAgent::set_target_function(const std::string &function_name) {
  uintptr_t address;
  if (!resolve_function_address(function_name, address)) {
    std::cerr << "[AGENT] Failed to resolve function: " << function_name
              << std::endl;
    return false;
  }

  return set_target_function(address);
}

bool FuzzAgent::set_target_function(uintptr_t function_address) {
  if (!agent_utils::is_function_address_valid(function_address)) {
    std::cerr << "[AGENT] Invalid function address: 0x" << std::hex
              << function_address << std::endl;
    return false;
  }

  target_function_ = reinterpret_cast<TargetFunction>(function_address);
  target_function_address_ = function_address;

  std::cout << "[AGENT] Target function set to 0x" << std::hex
            << function_address << std::endl;

  // Instrument function for coverage if needed
  instrument_function(function_address);

  return true;
}

AgentExecutionResult FuzzAgent::execute_input(const uint8_t *data,
                                              size_t size) {
  AgentExecutionResult result = {};
  result.result = AgentResponse::ERROR_;

  if (!target_function_) {
    std::cerr << "[AGENT] No target function set" << std::endl;
    return result;
  }

  auto start_time = std::chrono::high_resolution_clock::now();

  // Execute with crash detection
  bool success = execute_with_crash_detection(data, size, result);

  auto end_time = std::chrono::high_resolution_clock::now();
  result.execution_time_us =
      std::chrono::duration_cast<std::chrono::microseconds>(end_time -
                                                            start_time)
          .count();

  if (success) {
    result.result = AgentResponse::SUCCESS;
    total_executions_++;
  }

  return result;
}

void FuzzAgent::reset_coverage() {
  coverage_count_.store(0);
  coverage_points_.clear();
  memset(shared_memory_->coverage.coverage_bitmap, 0,
         sizeof(shared_memory_->coverage.coverage_bitmap));
}

CoverageInfo FuzzAgent::get_coverage() const {
  CoverageInfo info = {};
  info.basic_block_count = coverage_count_.load();
  info.edge_count = coverage_points_.size();
  info.function_count = target_function_ ? 1 : 0;

  // Copy coverage bitmap
  memcpy(info.coverage_bitmap, shared_memory_->coverage.coverage_bitmap,
         sizeof(info.coverage_bitmap));

  return info;
}

void FuzzAgent::signal_handler(int sig) {
  if (current_agent_ && in_target_execution_) {
    current_agent_->handle_crash(sig);
    siglongjmp(crash_jmp_buf_, sig);
  }
}

void FuzzAgent::setup_signal_handlers() {
  signal(SIGSEGV, signal_handler);
  signal(SIGFPE, signal_handler);
  signal(SIGILL, signal_handler);
  signal(SIGABRT, signal_handler);
  signal(SIGBUS, signal_handler);
}

bool FuzzAgent::create_shared_memory() {
#ifdef _WIN32
  // Windows implementation would go here
  return false;
#else
  // Create shared memory object
  shm_fd_ = shm_open(shm_name_.c_str(), O_CREAT | O_RDWR, 0666);
  if (shm_fd_ == -1) {
    perror("shm_open");
    return false;
  }

  // Set size
  if (ftruncate(shm_fd_, sizeof(SharedMemoryLayout)) == -1) {
    perror("ftruncate");
    close(shm_fd_);
    return false;
  }

  // Map memory
  shared_memory_ = static_cast<SharedMemoryLayout *>(
      mmap(nullptr, sizeof(SharedMemoryLayout), PROT_READ | PROT_WRITE,
           MAP_SHARED, shm_fd_, 0));

  if (shared_memory_ == MAP_FAILED) {
    perror("mmap");
    close(shm_fd_);
    return false;
  }

  return true;
#endif
}

void FuzzAgent::cleanup_shared_memory() {
#ifndef _WIN32
  if (shared_memory_) {
    munmap(shared_memory_, sizeof(SharedMemoryLayout));
    shared_memory_ = nullptr;
  }

  if (shm_fd_ != -1) {
    close(shm_fd_);
    shm_unlink(shm_name_.c_str());
    shm_fd_ = -1;
  }
#endif
}

bool FuzzAgent::resolve_function_address(const std::string &function_name,
                                         uintptr_t &address) {
#ifdef _WIN32
  // Windows implementation using GetProcAddress
  HMODULE handle = GetModuleHandle(nullptr);
  if (!handle) {
    std::cerr << "[AGENT] GetModuleHandle failed" << std::endl;
    return false;
  }

  FARPROC symbol = GetProcAddress(handle, function_name.c_str());
  if (!symbol) {
    std::cerr << "[AGENT] GetProcAddress failed for " << function_name
              << std::endl;
    return false;
  }

  address = reinterpret_cast<uintptr_t>(symbol);
  std::cout << "[AGENT] Resolved " << function_name << " to 0x" << std::hex
            << address << std::endl;
  return true;
#else
  // Try to resolve using dlsym
  void *handle = dlopen(nullptr, RTLD_LAZY);
  if (!handle) {
    std::cerr << "[AGENT] dlopen failed: " << dlerror() << std::endl;
    return false;
  }

  void *symbol = dlsym(handle, function_name.c_str());
  if (!symbol) {
    std::cerr << "[AGENT] dlsym failed for " << function_name << ": "
              << dlerror() << std::endl;
    dlclose(handle);
    return false;
  }

  address = reinterpret_cast<uintptr_t>(symbol);
  dlclose(handle);

  std::cout << "[AGENT] Resolved " << function_name << " to 0x" << std::hex
            << address << std::endl;
  return true;
#endif
}

void FuzzAgent::handle_command() {
  AgentCommand cmd = shared_memory_->command.load();

  switch (cmd) {
  case AgentCommand::PING:
    send_response(AgentResponse::SUCCESS);
    break;

  case AgentCommand::SET_TARGET_FUNCTION:
    if (strlen(shared_memory_->target_function_name) > 0) {
      bool success = set_target_function(shared_memory_->target_function_name);
      send_response(success ? AgentResponse::SUCCESS
                            : AgentResponse::FUNCTION_NOT_FOUND);
    } else if (shared_memory_->target_function_address != 0) {
      bool success =
          set_target_function(shared_memory_->target_function_address);
      send_response(success ? AgentResponse::SUCCESS : AgentResponse::ERROR_);
    } else {
      send_response(AgentResponse::ERROR_);
    }
    break;

  case AgentCommand::EXECUTE_INPUT: {
    AgentExecutionResult result =
        execute_input(shared_memory_->input_data, shared_memory_->input_size);
    shared_memory_->exec_result = result;
    send_response(result.result);
  } break;

  case AgentCommand::GET_COVERAGE:
    shared_memory_->coverage = get_coverage();
    send_response(AgentResponse::SUCCESS);
    break;

  case AgentCommand::RESET_COVERAGE:
    reset_coverage();
    send_response(AgentResponse::SUCCESS);
    break;

  case AgentCommand::GET_STATS:
    update_statistics();
    send_response(AgentResponse::SUCCESS);
    break;

  case AgentCommand::SHUTDOWN:
    should_stop_ = true;
    send_response(AgentResponse::SUCCESS);
    break;

  default:
    send_response(AgentResponse::ERROR_);
    break;
  }
}

void FuzzAgent::send_response(AgentResponse response) {
  shared_memory_->response.store(response);
  shared_memory_->response_ready.store(true);
}

void FuzzAgent::update_statistics() {
  shared_memory_->total_executions = total_executions_.load();
  shared_memory_->total_crashes = total_crashes_.load();
  shared_memory_->total_timeouts = total_timeouts_.load();
}

void FuzzAgent::instrument_function(uintptr_t function_address) {
  // Simple instrumentation - add coverage point
  add_coverage_point(function_address);
}

void FuzzAgent::add_coverage_point(uintptr_t address) {
  coverage_points_.push_back(address);
  coverage_count_++;

  // Update bitmap
  size_t index =
      (address >> 4) % (sizeof(shared_memory_->coverage.coverage_bitmap) * 8);
  size_t byte_index = index / 64;
  size_t bit_index = index % 64;

  shared_memory_->coverage.coverage_bitmap[byte_index] |= (1ULL << bit_index);
}

void FuzzAgent::handle_crash(int signal) {
  total_crashes_++;

  std::cout << "[AGENT] Crash detected with signal " << signal << std::endl;
}

bool FuzzAgent::execute_with_crash_detection(const uint8_t *data, size_t size,
                                             AgentExecutionResult &result) {
  if (sigsetjmp(crash_jmp_buf_, 1) != 0) {
    // We jumped here from a signal handler
    result.result = AgentResponse::CRASH;
    result.signal_number =
        errno; // Signal number stored in errno by signal handler
    in_target_execution_ = false;
    return false;
  }

  in_target_execution_ = true;

  try {
    result.return_value = target_function_(data, size);
    result.result = AgentResponse::SUCCESS;
    in_target_execution_ = false;
    return true;
  } catch (...) {
    result.result = AgentResponse::CRASH;
    in_target_execution_ = false;
    return false;
  }
}

// C interface implementation
extern "C" {
static std::unique_ptr<fuzzer::FuzzAgent> g_agent;
static std::thread g_agent_thread;

void fuzz_agent_init() {
  std::cout << "[AGENT] Library loaded into process " << getpid() << std::endl;

  // Create shared memory name based on PID
  std::string shm_name = "/fuzz_agent_" + std::to_string(getpid());

  g_agent = std::make_unique<fuzzer::FuzzAgent>();
  if (g_agent->initialize(shm_name)) {
    // Start agent in background thread
    g_agent_thread = std::thread([&]() { g_agent->run(); });

    std::cout << "[AGENT] Agent started successfully" << std::endl;
  } else {
    std::cerr << "[AGENT] Failed to initialize agent" << std::endl;
    g_agent.reset();
  }
}

void fuzz_agent_cleanup() {
  std::cout << "[AGENT] Library unloading from process " << getpid()
            << std::endl;

  if (g_agent) {
    g_agent->shutdown();

    if (g_agent_thread.joinable()) {
      g_agent_thread.join();
    }

    g_agent.reset();
  }
}

int fuzz_agent_start(const char *shm_name) {
  if (g_agent) {
    std::cerr << "[AGENT] Agent already running" << std::endl;
    return 0;
  }

  g_agent = std::make_unique<fuzzer::FuzzAgent>();
  if (!g_agent->initialize(shm_name)) {
    g_agent.reset();
    return 0;
  }

  g_agent_thread = std::thread([&]() { g_agent->run(); });

  return 1;
}

void fuzz_agent_stop() {
  if (g_agent) {
    g_agent->shutdown();

    if (g_agent_thread.joinable()) {
      g_agent_thread.join();
    }

    g_agent.reset();
  }
}

int fuzz_agent_execute(const uint8_t *data, size_t size) {
  if (!g_agent) {
    return -1;
  }

  auto result = g_agent->execute_input(data, size);
  return static_cast<int>(result.result);
}
}
} // namespace fuzzer

// Utility functions implementation
namespace fuzzer {
namespace agent_utils {

std::string get_process_name() {
  char buffer[256];
  if (readlink("/proc/self/exe", buffer, sizeof(buffer) - 1) != -1) {
    buffer[sizeof(buffer) - 1] = '\0';
    std::string path(buffer);
    size_t pos = path.find_last_of('/');
    if (pos != std::string::npos) {
      return path.substr(pos + 1);
    }
    return path;
  }
  return "unknown";
}

pid_t get_process_id() { return getpid(); }

std::vector<std::string> get_loaded_modules() {
  std::vector<std::string> modules;

  FILE *maps = fopen("/proc/self/maps", "r");
  if (!maps)
    return modules;

  char line[1024];
  std::set<std::string> unique_modules;

  while (fgets(line, sizeof(line), maps)) {
    char *path = strchr(line, '/');
    if (path) {
      char *newline = strchr(path, '\n');
      if (newline)
        *newline = '\0';
      unique_modules.insert(path);
    }
  }

  fclose(maps);

  for (const auto &module : unique_modules) {
    modules.push_back(module);
  }

  return modules;
}

bool is_function_address_valid(uintptr_t address) {
  // Simple validation - check if address is in executable memory
  FILE *maps = fopen("/proc/self/maps", "r");
  if (!maps)
    return false;

  char line[1024];
  bool valid = false;

  while (fgets(line, sizeof(line), maps)) {
    uintptr_t start, end;
    char perms[5];

    if (sscanf(line, "%lx-%lx %4s", &start, &end, perms) == 3) {
      if (address >= start && address < end && perms[2] == 'x') {
        valid = true;
        break;
      }
    }
  }

  fclose(maps);
  return valid;
}

std::string get_function_name_at_address(uintptr_t address) {
  Dl_info info;
  if (dladdr(reinterpret_cast<void *>(address), &info) && info.dli_sname) {
    return std::string(info.dli_sname);
  }
  return "unknown";
}

uintptr_t resolve_symbol(const std::string &symbol_name) {
  void *handle = dlopen(nullptr, RTLD_LAZY);
  if (!handle)
    return 0;

  void *symbol = dlsym(handle, symbol_name.c_str());
  dlclose(handle);

  return reinterpret_cast<uintptr_t>(symbol);
}

uintptr_t resolve_symbol_in_module(const std::string &symbol_name,
                                   const std::string &module_name) {
  void *handle = dlopen(module_name.c_str(), RTLD_LAZY);
  if (!handle)
    return 0;

  void *symbol = dlsym(handle, symbol_name.c_str());
  dlclose(handle);

  return reinterpret_cast<uintptr_t>(symbol);
}

bool make_memory_executable(void *address, size_t size) {
  uintptr_t page_start =
      reinterpret_cast<uintptr_t>(address) & ~(getpagesize() - 1);
  size_t page_size = ((reinterpret_cast<uintptr_t>(address) + size -
                       page_start + getpagesize() - 1) /
                      getpagesize()) *
                     getpagesize();

  return mprotect(reinterpret_cast<void *>(page_start), page_size,
                  PROT_READ | PROT_WRITE | PROT_EXEC) == 0;
}

bool protect_memory(void *address, size_t size, int protection) {
  uintptr_t page_start =
      reinterpret_cast<uintptr_t>(address) & ~(getpagesize() - 1);
  size_t page_size = ((reinterpret_cast<uintptr_t>(address) + size -
                       page_start + getpagesize() - 1) /
                      getpagesize()) *
                     getpagesize();

  return mprotect(reinterpret_cast<void *>(page_start), page_size,
                  protection) == 0;
}

} // namespace agent_utils
} // namespace fuzzer
