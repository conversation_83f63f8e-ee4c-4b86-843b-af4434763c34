# Linux 32-bit Process Fuzzer

A full-featured fuzzer designed for Linux 32-bit systems with process injection capabilities and function-level targeting. This fuzzer can attach to running processes, inject into them, and target specific functions for comprehensive security testing.

## Features

### Core Fuzzing Capabilities
- **Process Injection**: Attach to running processes using ptrace
- **Function Targeting**: Target specific functions by name or address
- **Coverage-Guided Fuzzing**: Track code coverage to guide mutation strategies
- **Crash Detection**: Detect and categorize crashes with reproduction information
- **Corpus Management**: Intelligent seed management with fitness scoring
- **Multiple Mutation Strategies**: Advanced mutation algorithms for effective fuzzing

### Advanced Features
- **Seed Reproduction**: Track mutation sequences to reproduce crashes
- **Coverage Tracking**: Basic block and edge coverage analysis
- **Symbol Resolution**: Automatic function discovery and symbol resolution
- **ELF32 Parsing**: Native support for 32-bit ELF binaries
- **Memory Analysis**: Process memory mapping and analysis
- **Crash Minimization**: Automatic crash input minimization

## Architecture

The fuzzer consists of several key components:

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Main CLI      │    │   Fuzzer Core   │    │   Process       │
│   Interface     │───▶│   Engine        │───▶│   Injector      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │                         │
                              ▼                         ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Corpus        │    │   Coverage      │    │   Target        │
│   Management    │    │   Tracking      │    │   Resolver      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Components

1. **Fuzzer Core** (`fuzzer.hpp/cpp`): Main fuzzing engine and orchestration
2. **Process Injector** (`injector.hpp/cpp`): Process attachment and memory manipulation
3. **Corpus Management** (`corpus.hpp/cpp`): Seed management and mutation engine
4. **Coverage Tracking** (`coverage.hpp`): Code coverage analysis (header only for now)
5. **Target Resolver** (`target_resolver.hpp`): Function discovery and symbol resolution

## Building

### Prerequisites

For Ubuntu/Debian systems:
```bash
# Install build dependencies
make install-deps

# Or manually:
sudo apt-get install build-essential gcc-multilib g++-multilib libc6-dev-i386
```

### Compilation

```bash
# Build everything
make

# Build specific targets
make fuzzer      # Main fuzzer executable
make test_target # Test target program
make debug       # Debug build
make release     # Optimized release build
```

## Usage

### Basic Usage

```bash
# Attach to running process and fuzz specific function
./fuzzer -p <PID> -f <function_name>

# Fuzz with specific function address
./fuzzer -p <PID> -a 0x08048000

# Use custom corpus and output directories
./fuzzer -p <PID> -f main -c ./my_corpus -o ./my_output

# Load seed files
./fuzzer -p <PID> -f vulnerable_func -s seed1.bin -s seed2.bin
```

### Command Line Options

```
Options:
  -p, --pid PID              Target process PID
  -b, --binary PATH          Target binary path
  -f, --function NAME        Target function name
  -a, --address ADDR         Target function address (hex)
  -c, --corpus DIR           Corpus directory (default: ./corpus)
  -o, --output DIR           Output directory (default: ./output)
  -s, --seed FILE            Seed file(s) to load
  -i, --iterations NUM       Maximum iterations (default: 1000000)
  -t, --timeout MS           Timeout in milliseconds (default: 1000)
  -v, --verbose              Verbose output
  --no-coverage              Disable coverage tracking
  -h, --help                 Show help
```

### Testing with Provided Target

1. **Build the test target:**
   ```bash
   make test_target
   ```

2. **Run the test target:**
   ```bash
   ./test_target &
   ```

3. **Note the PID and function addresses displayed**

4. **Run the fuzzer:**
   ```bash
   ./fuzzer -p <PID> -f fuzz_target
   ```

### Example Workflow

```bash
# Terminal 1: Start test target
./test_target
# Note: PID: 12345, fuzz_target address: 0x08048abc

# Terminal 2: Run fuzzer
./fuzzer -p 12345 -f fuzz_target -v

# Or target specific address
./fuzzer -p 12345 -a 0x08048abc -c ./corpus -i 100000
```

## Output

The fuzzer creates several output directories:

- **corpus/**: Seed files and interesting inputs
- **crashes/**: Crash-inducing inputs with reproduction info
- **output/**: General output and logs

### Crash Files

Crash files are saved with the format: `crash_<hash>.bin`
Each crash includes:
- The input that caused the crash
- Signal information
- Reproduction metadata

### Statistics

The fuzzer provides real-time statistics:
```
=== Fuzzing Statistics ===
Executions: 50000
Exec/sec: 1250.50
Crashes: 5 (unique: 3)
Timeouts: 12
New coverage: 45
===========================
```

## Reproduction

To reproduce a crash:

1. **Find the crash file** in the crashes directory
2. **Use the reproduction information** stored with the crash
3. **Run with the same seed and mutation sequence**

```bash
# Example: Reproduce crash manually
./test_target "$(cat crashes/crash_signal11_64.bin)"
```

## Advanced Usage

### Custom Seed Files

Create seed files with interesting inputs:
```bash
# Create a seed file
echo -ne '\xDE\xAD\xBE\xEF\x41\x41\x41\x41' > seed_deadbeef.bin

# Use with fuzzer
./fuzzer -p <PID> -f fuzz_target -s seed_deadbeef.bin
```

### Coverage-Guided Fuzzing

The fuzzer automatically tracks coverage when enabled (default):
- Basic block coverage
- Edge coverage
- Coverage-guided mutations
- Automatic corpus expansion

### Function Discovery

The fuzzer can automatically discover functions:
```bash
# List available functions (future feature)
./fuzzer -p <PID> --list-functions

# Fuzz multiple functions (future feature)
./fuzzer -p <PID> -f "func1,func2,func3"
```

## Limitations

### Current Limitations

1. **Linux Only**: Currently supports Linux systems only
2. **32-bit Focus**: Optimized for 32-bit targets
3. **Function Calling**: Simplified function calling convention
4. **Coverage Implementation**: Basic coverage tracking (full implementation pending)
5. **Windows Support**: Not yet implemented

### Future Enhancements

- Full coverage tracking implementation
- Windows process injection support
- 64-bit target support
- Network fuzzing capabilities
- Distributed fuzzing
- GUI interface

## Security Considerations

### Permissions

The fuzzer requires appropriate permissions to:
- Attach to processes (ptrace capability)
- Read process memory
- Write to process memory
- Set breakpoints

### Safety

- Always test on isolated systems
- Use appropriate process isolation
- Monitor system resources
- Have crash recovery procedures

## Troubleshooting

### Common Issues

1. **Permission Denied**
   ```bash
   # Enable ptrace for non-root users
   echo 0 | sudo tee /proc/sys/kernel/yama/ptrace_scope
   ```

2. **Process Not Found**
   ```bash
   # Check if process is running
   ps aux | grep <process_name>
   ```

3. **Function Not Found**
   ```bash
   # Check available symbols
   nm -D <binary> | grep <function_name>
   objdump -t <binary> | grep <function_name>
   ```

4. **Build Errors**
   ```bash
   # Install 32-bit development libraries
   sudo apt-get install libc6-dev-i386 gcc-multilib
   ```

### Debug Mode

Build and run in debug mode for detailed output:
```bash
make debug
./fuzzer -p <PID> -f <function> -v
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is provided for educational and security research purposes.

## Disclaimer

This tool is intended for authorized security testing only. Users are responsible for ensuring they have proper authorization before testing any systems.
