#include "injector.hpp"

#include <iostream>
#include <fstream>
#include <sstream>
#include <vector>
#include <cstring>

#ifdef _WIN32
    #include <windows.h>
    #include <tlhelp32.h>
    #include <psapi.h>
#else
    #include <sys/ptrace.h>
    #include <sys/wait.h>
    #include <sys/user.h>
    #include <unistd.h>
    #include <signal.h>
    #include <elf.h>
    #include <link.h>
    #include <dlfcn.h>
#endif

namespace fuzzer {

ProcessInjector::~ProcessInjector() {
    if (is_attached()) {
        detach();
    }
}

bool ProcessInjector::attach(process_id_t pid) {
    if (is_attached()) {
        std::cerr << "[-] Already attached to a process" << std::endl;
        return false;
    }

#ifdef _WIN32
    // Windows implementation would go here
    std::cerr << "[-] Windows process injection not implemented yet" << std::endl;
    return false;
#else
    if (ptrace_attach(pid)) {
        attached_pid_ = pid;
        std::cout << "[+] Successfully attached to process " << pid << std::endl;
        return true;
    }
    return false;
#endif
}

bool ProcessInjector::detach() {
    if (!is_attached()) {
        return true;
    }

#ifdef _WIN32
    // Windows implementation
    return false;
#else
    bool success = ptrace_detach(attached_pid_);
    if (success) {
        attached_pid_ = -1;
        std::cout << "[+] Detached from process" << std::endl;
    }
    return success;
#endif
}

bool ProcessInjector::read_memory(uintptr_t address, void* buffer, size_t size) {
    if (!is_attached()) {
        return false;
    }

#ifdef _WIN32
    // Windows implementation
    return false;
#else
    uint8_t* buf = static_cast<uint8_t*>(buffer);
    for (size_t i = 0; i < size; i += WORD_SIZE) {
        errno = 0;
        long word = ptrace(PTRACE_PEEKDATA, attached_pid_, address + i, nullptr);
        if (errno != 0) {
            return false;
        }

        size_t copy_size = std::min(WORD_SIZE, size - i);
        memcpy(buf + i, &word, copy_size);
    }
    return true;
#endif
}

bool ProcessInjector::write_memory(uintptr_t address, const void* data, size_t size) {
    if (!is_attached()) {
        return false;
    }

#ifdef _WIN32
    // Windows implementation
    return false;
#else
    const uint8_t* buf = static_cast<const uint8_t*>(data);
    for (size_t i = 0; i < size; i += WORD_SIZE) {
        uint32_t word = 0;

        // Read existing word if we're not writing a full word
        if (i + WORD_SIZE > size) {
            errno = 0;
            word = ptrace(PTRACE_PEEKDATA, attached_pid_, address + i, nullptr);
            if (errno != 0) {
                return false;
            }
        }

        // Copy new data into word
        size_t copy_size = std::min(WORD_SIZE, size - i);
        memcpy(&word, buf + i, copy_size);

        // Write word back
        if (ptrace(PTRACE_POKEDATA, attached_pid_, address + i, word) == -1) {
            return false;
        }
    }
    return true;
#endif
}

bool ProcessInjector::set_breakpoint(uintptr_t address) {
    if (!is_attached()) {
        return false;
    }

#ifdef _WIN32
    return false;
#else
    // Save original byte
    uint8_t original_byte;
    if (!read_memory(address, &original_byte, 1)) {
        return false;
    }

    original_bytes_[address] = original_byte;

    // Write INT3 instruction
    uint8_t int3 = INT3_OPCODE;
    return write_memory(address, &int3, 1);
#endif
}

bool ProcessInjector::remove_breakpoint(uintptr_t address) {
    if (!is_attached()) {
        return false;
    }

#ifdef _WIN32
    return false;
#else
    auto it = original_bytes_.find(address);
    if (it == original_bytes_.end()) {
        return false;
    }

    // Restore original byte
    bool success = write_memory(address, &it->second, 1);
    if (success) {
        original_bytes_.erase(it);
    }
    return success;
#endif
}

bool ProcessInjector::continue_execution() {
    if (!is_attached()) {
        return false;
    }

#ifdef _WIN32
    return false;
#else
    return ptrace(PTRACE_CONT, attached_pid_, nullptr, nullptr) == 0;
#endif
}

bool ProcessInjector::wait_for_signal(int& signal, int& status) {
    if (!is_attached()) {
        return false;
    }

#ifdef _WIN32
    return false;
#else
    pid_t result = waitpid(attached_pid_, &status, 0);
    if (result == attached_pid_) {
        if (WIFSTOPPED(status)) {
            signal = WSTOPSIG(status);
            return true;
        }
    }
    return false;
#endif
}

bool ProcessInjector::get_registers(struct user_regs_struct& regs) {
    if (!is_attached()) {
        return false;
    }

#ifdef _WIN32
    // Windows implementation would go here
    memset(&regs, 0, sizeof(regs));
    return false;
#else
    return ptrace(PTRACE_GETREGS, attached_pid_, nullptr, &regs) == 0;
#endif
}

bool ProcessInjector::set_registers(const struct user_regs_struct& regs) {
    if (!is_attached()) {
        return false;
    }

#ifdef _WIN32
    // Windows implementation would go here
    return false;
#else
    return ptrace(PTRACE_SETREGS, attached_pid_, nullptr, &regs) == 0;
#endif
}

bool ProcessInjector::call_function(uintptr_t function_address, const std::vector<uint8_t>& args,
                                   uint32_t& return_value, uint32_t timeout_ms) {
    if (!is_attached()) {
        return false;
    }

#ifdef _WIN32
    // Windows implementation would go here
    return false;
#else
    // Save current registers
    struct user_regs_struct original_regs, new_regs;
    if (!get_registers(original_regs)) {
        return false;
    }

    new_regs = original_regs;

    // Allocate memory for arguments in target process
    uintptr_t arg_memory = 0;
    if (!args.empty()) {
        arg_memory = allocate_memory_in_target(args.size());
        if (arg_memory == 0) {
            return false;
        }

        // Write arguments to target memory
        if (!write_memory(arg_memory, args.data(), args.size())) {
            free_memory_in_target(arg_memory, args.size());
            return false;
        }
    }

    // Set up function call (x86-32 calling convention)
    // Push arguments onto stack (right to left for cdecl)
    uint32_t stack_ptr = new_regs.esp;

    // Push arguments: data pointer and size for fuzz_target(const uint8_t* data, size_t size)
    if (!args.empty()) {
        stack_ptr -= 4; // size_t size
        uint32_t size_val = args.size();
        if (!write_memory(stack_ptr, &size_val, 4)) {
            free_memory_in_target(arg_memory, args.size());
            return false;
        }

        stack_ptr -= 4; // const uint8_t* data
        uint32_t data_ptr = arg_memory;
        if (!write_memory(stack_ptr, &data_ptr, 4)) {
            free_memory_in_target(arg_memory, args.size());
            return false;
        }
    }

    // Push return address (we'll use a breakpoint here)
    stack_ptr -= 4;
    uint32_t return_addr = 0xDEADBEEF; // Dummy return address
    if (!write_memory(stack_ptr, &return_addr, 4)) {
        if (arg_memory) free_memory_in_target(arg_memory, args.size());
        return false;
    }

    // Set registers for function call
    new_regs.esp = stack_ptr;
    new_regs.eip = function_address;

    if (!set_registers(new_regs)) {
        if (arg_memory) free_memory_in_target(arg_memory, args.size());
        return false;
    }

    // Execute and wait for completion
    int exit_signal = 0;
    bool success = execute_and_wait(timeout_ms, exit_signal);

    // Get return value from EAX
    struct user_regs_struct final_regs;
    if (get_registers(final_regs)) {
        return_value = final_regs.eax;
    }

    // Restore original registers
    set_registers(original_regs);

    // Clean up allocated memory
    if (arg_memory) {
        free_memory_in_target(arg_memory, args.size());
    }

    return success && (exit_signal == 0 || exit_signal == SIGTRAP);
#endif
}

bool ProcessInjector::execute_and_wait(uint32_t timeout_ms, int& exit_signal) {
    if (!is_attached()) {
        return false;
    }

#ifdef _WIN32
    return false;
#else
    // Continue execution
    if (!continue_execution()) {
        return false;
    }

    // Wait for signal with timeout
    int status;

    // Simple timeout implementation (not perfect but functional)
    for (uint32_t i = 0; i < timeout_ms; i += 10) {
        pid_t result = waitpid(attached_pid_, &status, WNOHANG);

        if (result == attached_pid_) {
            if (WIFSTOPPED(status)) {
                exit_signal = WSTOPSIG(status);
                return true;
            } else if (WIFSIGNALED(status)) {
                exit_signal = WTERMSIG(status);
                return false; // Process terminated
            }
        } else if (result == -1) {
            return false; // Error
        }

        // Sleep for 10ms
        usleep(10000);
    }

    // Timeout occurred
    exit_signal = SIGALRM;
    return false;
#endif
}

uintptr_t ProcessInjector::allocate_memory_in_target(size_t size) {
    if (!is_attached()) {
        return 0;
    }

#ifdef _WIN32
    return 0;
#else
    // Simple implementation: find a free memory region
    // In a real implementation, you'd use mmap syscall injection

    // For now, use a simple approach: find unused memory in the process
    uintptr_t base_addr = 0x10000000; // Start looking from 256MB

    // Try to find a free region by attempting to read memory
    for (uintptr_t addr = base_addr; addr < base_addr + 0x10000000; addr += 0x1000) {
        uint8_t test_byte;
        if (!read_memory(addr, &test_byte, 1)) {
            // This region seems to be unmapped, we can potentially use it
            // In a real implementation, we'd inject mmap syscall here
            return addr;
        }
    }

    return 0; // Failed to find free memory
#endif
}

bool ProcessInjector::free_memory_in_target(uintptr_t address, size_t size) {
    // Simple implementation - in real code, would inject munmap syscall
    (void)address;
    (void)size;
    return true;
}

#ifndef _WIN32
bool ProcessInjector::ptrace_attach(process_id_t pid) {
    if (ptrace(PTRACE_ATTACH, pid, nullptr, nullptr) == -1) {
        perror("ptrace attach failed");
        return false;
    }

    // Wait for the process to stop
    int status;
    if (waitpid(pid, &status, 0) == -1) {
        perror("waitpid failed");
        ptrace(PTRACE_DETACH, pid, nullptr, nullptr);
        return false;
    }

    if (!WIFSTOPPED(status)) {
        std::cerr << "[-] Process did not stop after attach" << std::endl;
        ptrace(PTRACE_DETACH, pid, nullptr, nullptr);
        return false;
    }

    return true;
}

bool ProcessInjector::ptrace_detach(process_id_t pid) {
    return ptrace(PTRACE_DETACH, pid, nullptr, nullptr) == 0;
}
#endif

// Utility functions
std::string get_process_name(process_id_t pid) {
#ifdef _WIN32
    return "unknown";
#else
    std::string path = "/proc/" + std::to_string(pid) + "/comm";
    std::ifstream file(path);
    std::string name;
    if (file >> name) {
        return name;
    }
    return "unknown";
#endif
}

std::string get_process_path(process_id_t pid) {
#ifdef _WIN32
    return "unknown";
#else
    std::string path = "/proc/" + std::to_string(pid) + "/exe";
    char buffer[1024];
    ssize_t len = readlink(path.c_str(), buffer, sizeof(buffer) - 1);
    if (len != -1) {
        buffer[len] = '\0';
        return std::string(buffer);
    }
    return "unknown";
#endif
}

std::vector<process_id_t> find_processes_by_name(const std::string& name) {
    std::vector<process_id_t> pids;

#ifdef _WIN32
    // Windows implementation would go here
#else
    // Simple implementation - scan /proc
    for (int pid = 1; pid < 65536; ++pid) {
        if (get_process_name(pid) == name) {
            pids.push_back(pid);
        }
    }
#endif

    return pids;
}

bool is_process_running(process_id_t pid) {
#ifdef _WIN32
    return false;
#else
    return kill(pid, 0) == 0;
#endif
}

// ELF32Parser implementation
ELF32Parser::ELF32Parser(const std::string& binary_path)
    : binary_path_(binary_path) {
}

bool ELF32Parser::parse() {
#ifdef _WIN32
    return false;
#else
    std::ifstream file(binary_path_, std::ios::binary);
    if (!file) {
        return false;
    }

    // Read ELF header
    Elf32_Ehdr header;
    file.read(reinterpret_cast<char*>(&header), sizeof(header));

    if (file.gcount() != sizeof(header)) {
        return false;
    }

    // Verify ELF magic
    if (memcmp(header.e_ident, ELFMAG, SELFMAG) != 0) {
        return false;
    }

    // Verify 32-bit
    if (header.e_ident[EI_CLASS] != ELFCLASS32) {
        return false;
    }

    entry_point_ = header.e_entry;
    valid_ = true;

    return parse_section_headers() && parse_symbol_table();
#endif
}

uintptr_t ELF32Parser::get_symbol_address(const std::string& symbol_name) {
    for (const auto& symbol : symbols_) {
        if (symbol.name == symbol_name) {
            return symbol.address;
        }
    }
    return 0;
}

#ifndef _WIN32
bool ELF32Parser::parse_section_headers() {
    // Simplified implementation
    return true;
}

bool ELF32Parser::parse_symbol_table() {
    // Simplified implementation
    return true;
}
#endif

} // namespace fuzzer
