#!/bin/bash

# Script to create initial seed files for fuzzing

echo "Creating seed files for fuzzing..."

# Create corpus directory
mkdir -p corpus

# Seed 1: Simple string
echo -n "Hello World" > corpus/seed_hello.bin

# Seed 2: Crash trigger pattern (DEADBEEF)
echo -ne '\xDE\xAD\xBE\xEF' > corpus/seed_deadbeef.bin

# Seed 3: Buffer overflow pattern
echo -ne 'AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA' > corpus/seed_overflow.bin

# Seed 4: Format string pattern
echo -ne '%x%x%x%x%x%x%x%x' > corpus/seed_format.bin

# Seed 5: Integer overflow pattern
echo -ne '\xFF\xFF\xFF\xFF' > corpus/seed_intoverflow.bin

# Seed 6: Mixed binary data
echo -ne '\x00\x01\x02\x03\x04\x05\x06\x07\x08\x09\x0A\x0B\x0C\x0D\x0E\x0F' > corpus/seed_binary.bin

# Seed 7: Large input
python3 -c "print('A' * 1024, end='')" > corpus/seed_large.bin

# Seed 8: Null bytes
echo -ne '\x00\x00\x00\x00\x00\x00\x00\x00' > corpus/seed_nulls.bin

# Seed 9: High bytes
echo -ne '\xFF\xFE\xFD\xFC\xFB\xFA\xF9\xF8' > corpus/seed_high.bin

# Seed 10: Specific crash pattern for test target
echo -ne '\x03\xDE\xAD\xBE\xEF\x41\x41\x41\x41' > corpus/seed_crash_trigger.bin

echo "Created seed files:"
ls -la corpus/

echo ""
echo "Seed file descriptions:"
echo "  seed_hello.bin       - Simple ASCII string"
echo "  seed_deadbeef.bin    - Crash trigger pattern"
echo "  seed_overflow.bin    - Buffer overflow pattern"
echo "  seed_format.bin      - Format string pattern"
echo "  seed_intoverflow.bin - Integer overflow pattern"
echo "  seed_binary.bin      - Mixed binary data"
echo "  seed_large.bin       - Large input (1024 bytes)"
echo "  seed_nulls.bin       - Null bytes"
echo "  seed_high.bin        - High byte values"
echo "  seed_crash_trigger.bin - Specific crash trigger for test target"

echo ""
echo "Usage examples:"
echo "  ./fuzzer -p <PID> -f fuzz_target -s corpus/seed_deadbeef.bin"
echo "  ./fuzzer -p <PID> -f fuzz_target -c corpus"
