cmake_minimum_required(VERSION 3.16)
project(fuzzing
  VERSION 1.0
  DESCRIPTION "Linux 32-bit Fuzzer"
  LANGUAGES CXX
)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Custom compiler path (if needed)
option(USE_CUSTOM_COMPILER "Use custom cross-compiler" OFF)
if(USE_CUSTOM_COMPILER)
    set(CMAKE_CXX_COMPILER "/home/<USER>/x-tools/i686-ubuntu16.04-linux-gnu/bin/i686-ubuntu16.04-linux-gnu-g++")
endif()

# Build type configuration
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE "Release" CACHE STRING "Build type" FORCE)
endif()

# Compiler flags
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wall -Wextra -m32")
set(CMAKE_CXX_FLAGS_DEBUG "-g -O0 -DDEBUG")
set(CMAKE_CXX_FLAGS_RELEASE "-O3 -DNDEBUG")
set(CMAKE_CXX_FLAGS_RELWITHDEBINFO "-O2 -g")

# Find required packages
find_package(Threads REQUIRED)

# Source files
set(FUZZER_SOURCES
    main.cpp
    injector.cpp
    fuzzer.cpp
    corpus.cpp
    coverage.cpp
    target_resolver.cpp
)

set(FUZZER_HEADERS
    fuzzer.hpp
    injector.hpp
    corpus.hpp
    coverage.hpp
    target_resolver.hpp
)

# Main fuzzer executable
add_executable(${PROJECT_NAME} ${FUZZER_SOURCES} ${FUZZER_HEADERS})
# Link libraries
target_link_libraries(${PROJECT_NAME}
  PRIVATE
  Threads::Threads
  #hacklib
)

# Set 32-bit architecture for both targets
set_target_properties(${PROJECT_NAME} PROPERTIES
    COMPILE_FLAGS "-m32"
    LINK_FLAGS "-m32"
)