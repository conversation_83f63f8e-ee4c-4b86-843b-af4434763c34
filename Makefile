# Makefile for Linux 32-bit Fuzzer
# Compile with: make
# Clean with: make clean

CXX = /home/<USER>/x-tools/i686-ubuntu16.04-linux-gnu/bin/i686-ubuntu16.04-linux-gnu-g++
#CXX = /home/<USER>/x-tools/x86_64-ubuntu16.04-linux-gnu/bin/x86_64-ubuntu16.04-linux-gnu-g++
CXXFLAGS = -std=c++17 -Wall -Wextra -O2 -g
LDFLAGS =
LIBS = -lpthread

# Target architecture (32-bit)
ARCH_FLAGS = -m32

# Source files
SOURCES = main.cpp fuzzer.cpp injector.cpp corpus.cpp coverage.cpp target_resolver.cpp
HEADERS = fuzzer.hpp injector.hpp corpus.hpp coverage.hpp target_resolver.hpp

# Library injection sources
LIB_SOURCES = fuzz_agent.cpp library_injector.cpp
LIB_HEADERS = fuzz_agent.hpp library_injector.hpp

# Object files
OBJECTS = $(SOURCES:.cpp=.o)
LIB_OBJECTS = $(LIB_SOURCES:.cpp=.o)

# Target executable
TARGET = fuzzer

# Shared library for injection
AGENT_LIB = libfuzz_agent.so

# Library-based fuzzer
LIB_FUZZER = lib_fuzzer

# Test target
TEST_TARGET = test_target

# Default target
all: $(TARGET) $(AGENT_LIB) $(LIB_FUZZER) $(TEST_TARGET)

# Main fuzzer executable
$(TARGET): $(OBJECTS)
	@echo "Linking $(TARGET)..."
	$(CXX) $(ARCH_FLAGS) $(LDFLAGS) -o $@ $^ $(LIBS)
	@echo "Build complete: $(TARGET)"

# Shared library for injection
$(AGENT_LIB): $(LIB_OBJECTS)
	@echo "Building shared library $(AGENT_LIB)..."
	$(CXX) $(ARCH_FLAGS) -shared -fPIC $(LDFLAGS) -o $@ $^ $(LIBS) -ldl -lrt
	@echo "Shared library complete: $(AGENT_LIB)"

# Library-based fuzzer
$(LIB_FUZZER): lib_main.o library_injector.o injector.o corpus.o
	@echo "Linking $(LIB_FUZZER)..."
	$(CXX) $(ARCH_FLAGS) $(LDFLAGS) -o $@ $^ $(LIBS) -lrt
	@echo "Build complete: $(LIB_FUZZER)"

# Library-based fuzzer
lib_main.o: lib_main.cpp $(HEADERS) $(LIB_HEADERS)
	@echo "Compiling $<..."
	$(CXX) $(ARCH_FLAGS) $(CXXFLAGS) -fPIC -c $< -o $@

# Object file compilation
%.o: %.cpp $(HEADERS) $(LIB_HEADERS)
	@echo "Compiling $<..."
	$(CXX) $(ARCH_FLAGS) $(CXXFLAGS) -fPIC -c $< -o $@

# Test target (vulnerable program for testing)
$(TEST_TARGET): test_target.cpp
	@echo "Building test target..."
	$(CXX) $(ARCH_FLAGS) $(CXXFLAGS) -o $@ $<

# Clean build artifacts
clean:
	@echo "Cleaning build artifacts..."
	rm -f $(OBJECTS) $(LIB_OBJECTS) $(TARGET) $(AGENT_LIB) $(LIB_FUZZER) $(TEST_TARGET)
	rm -f lib_main.o
	rm -rf corpus crashes output
	@echo "Clean complete"

# Install dependencies (Ubuntu/Debian)
install-deps:
	@echo "Installing build dependencies..."
	sudo apt-get update
	sudo apt-get install -y build-essential gcc-multilib g++-multilib libc6-dev-i386

# Create directories
dirs:
	mkdir -p corpus crashes output

# Run with default settings
run: $(TARGET) dirs
	./$(TARGET) --help

# Debug build
debug: CXXFLAGS += -DDEBUG -O0
debug: $(TARGET)

# Release build
release: CXXFLAGS += -DNDEBUG -O3
release: clean $(TARGET)

# Static analysis
analyze:
	@echo "Running static analysis..."
	cppcheck --enable=all --std=c++17 $(SOURCES) $(HEADERS)

# Format code
format:
	@echo "Formatting code..."
	clang-format -i $(SOURCES) $(HEADERS)

# Check for memory leaks (requires valgrind)
memcheck: $(TARGET)
	@echo "Running memory check..."
	valgrind --leak-check=full --show-leak-kinds=all ./$(TARGET) --help

# Package for distribution
package: clean
	@echo "Creating package..."
	tar -czf fuzzer.tar.gz *.cpp *.hpp Makefile README.md

# Help target
help:
	@echo "Available targets:"
	@echo "  all          - Build fuzzer and test target"
	@echo "  fuzzer       - Build main fuzzer executable"
	@echo "  test_target  - Build test target program"
	@echo "  clean        - Remove build artifacts"
	@echo "  install-deps - Install build dependencies"
	@echo "  dirs         - Create output directories"
	@echo "  run          - Run fuzzer with help"
	@echo "  debug        - Build debug version"
	@echo "  release      - Build optimized release version"
	@echo "  analyze      - Run static analysis"
	@echo "  format       - Format source code"
	@echo "  memcheck     - Run memory leak check"
	@echo "  package      - Create distribution package"
	@echo "  help         - Show this help"

# Phony targets
.PHONY: all clean install-deps dirs run debug release analyze format memcheck package help

# Build info
info:
	@echo "Fuzzer Build Information:"
	@echo "  Compiler: $(CXX)"
	@echo "  Flags: $(CXXFLAGS) $(ARCH_FLAGS)"
	@echo "  Sources: $(SOURCES)"
	@echo "  Target: $(TARGET)"
